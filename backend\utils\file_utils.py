"""
文件处理工具模块
"""
import os
from urllib.parse import unquote, urlparse


def get_filename_from_url(url: str) -> str:
    """从 URL 中提取文件名"""
    parsed_url = urlparse(url)
    filename = os.path.basename(unquote(parsed_url.path))
    return filename


def get_filename_from_headers(headers: dict) -> str:
    """从 Content-Disposition 头中提取文件名"""
    content_disposition = headers.get("Content-Disposition", "")
    if "filename=" in content_disposition:
        filename = content_disposition.split("filename=")[1].strip('"\'')
        return filename
    return None


def build_qa_prompt(dataset_name: str, dataset_sm: str, filename: str, base_prompt: str) -> str:
    """构建QA拆分提示词"""
    return f'''知识库名称是{dataset_name}，知识库说明是{dataset_sm}，文件名称是{filename}。
Role: 知识库内容优化专家和问答生成工程师
- Background: 用户需要对知识库内容进行优化，以便生成与文件内容强相关且准确的问答对。知识库包含产品介绍、功能、使用说明、接线图、部署和问题处理等多方面信息，用户期望从多维度生成最多50个问题，且问题和答案需避免重复和类似，确保内容的准确性和多样性。
- Profile: 你是一位精通知识库管理和问答系统构建的专家，对文本内容的拆分、理解和重组有着丰富的经验。你擅长从复杂的产品文档中提取关键信息，并以清晰、准确的方式生成问答对，确保每个问题都能精准地对应文件内容的某个方面。
- Skills: 你具备文本分析、信息提取、自然语言处理和问答系统设计的能力，能够高效地从产品文档中识别出不同维度的内容，并生成多样化的问答对，同时确保答案的完整性和准确性。
- Goals: 根据知识库说明、文件名称和文件内容，生成最多50个与文件内容强相关的问答对，确保问题和答案的准确性和多样性，避免重复和类似内容。
- Constrains: 生成的问答对需紧密围绕文件内容，确保答案详细完整且尽可能保留原文描述，问题需明确且具有针对性，避免模糊和重复。
- OutputFormat: 以问答对的形式输出，每个问题前需带上产品型号或产品名称，问题和答案需紧密相关且准确。
- Workflow:
1. 详细阅读和理解知识库说明、文件名称及文件内容，识别其中的关键信息和不同维度的内容。
2. 根据文件内容的不同部分（如产品介绍、功能、使用说明等），生成针对性的问题，并确保问题的多样性和准确性。
3. 对应每个问题，从文件内容中提取详细且准确的答案，尽可能保留原文描述，确保答案的完整性和准确性。
以下是知识库背景和示例： {base_prompt}
'''
