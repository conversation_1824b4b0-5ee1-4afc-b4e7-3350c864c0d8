#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库 66d517aee6ac063d23b738e9 的批量任务查询和索引同步脚本
"""

import json
import requests
import pandas as pd
from pymongo import MongoClient
from bson.objectid import ObjectId
from zhipuai import ZhipuAI
import datetime
import time
import os

# 配置信息
KNOWLEDGE_BASE_ID = "66d517aee6ac063d23b738e9"
BATCH_ID = "batch_1946134864269803520"
FILE_ID = "1752829683_7148fe2876df4a9bbc23d56a50fe5da2"
REQUEST_FILE = r"C:\Users\<USER>\Desktop\demo\batch_request_20250718_170724.jsonl"

# API 配置
client = ZhipuAI(api_key="d3e441ee4a536dd76c88a404ad8b05e4.t97wBPQIa5GuFT4y")

# MongoDB 配置
MONGO_URI = "***************************************************************************************************"

# FastGPT API 配置
FASTGPT_API_URL = 'http://**************:3000/api/core/dataset/data/update'
FASTGPT_HEADERS = {
    'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o',
    'Content-Type': 'application/json'
}


class LogDisplay:
    """简单的日志显示类"""
    
    def append(self, message):
        """打印日志消息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")


def check_batch_status_and_wait():
    """持续查询批量任务状态直到完成"""
    try:
        log_display = LogDisplay()
        log_display.append(f"开始持续查询批量任务状态: {BATCH_ID}")

        # 等待配置
        max_wait_time = 24 * 60 * 60  # 24小时
        check_interval = 30  # 30秒检查一次
        waited_time = 0

        while waited_time < max_wait_time:
            try:
                batch_status = client.batches.retrieve(BATCH_ID)
                status = batch_status.status

                log_display.append(f'当前状态: {status}')

                if status == "completed":
                    log_display.append('批量任务完成！')

                    # 显示最终统计信息
                    if hasattr(batch_status, 'request_counts'):
                        counts = batch_status.request_counts
                        log_display.append(f"最终统计: 总数={counts.total}, 完成={counts.completed}, 失败={counts.failed}")

                    return batch_status

                elif status in ["failed", "expired", "cancelled"]:
                    log_display.append(f'批量任务失败，状态: {status}')
                    return None

                # 显示进度信息
                if hasattr(batch_status, 'request_counts'):
                    counts = batch_status.request_counts
                    if counts.total > 0:
                        progress = (counts.completed / counts.total) * 100
                        log_display.append(f"进度: {progress:.1f}% ({counts.completed}/{counts.total})")

                # 等待一段时间后再次检查
                time.sleep(check_interval)
                waited_time += check_interval

                # 每5分钟显示一次等待时间
                if waited_time % 300 == 0:
                    log_display.append(f'已等待 {waited_time // 60} 分钟...')

            except Exception as e:
                log_display.append(f'查询状态时发生错误: {e}，等待后重试...')
                time.sleep(check_interval)
                waited_time += check_interval

        log_display.append('等待超时，批量任务可能仍在处理中')
        return None

    except Exception as e:
        log_display.append(f"查询批量任务状态失败: {e}")
        return None


def download_batch_results(batch_status):
    """下载批量任务结果"""
    try:
        log_display = LogDisplay()
        
        if batch_status.status != "completed":
            log_display.append(f"任务尚未完成，当前状态: {batch_status.status}")
            return None
            
        if not batch_status.output_file_id:
            log_display.append("没有找到输出文件")
            return None
            
        log_display.append("正在下载结果文件...")
        content = client.files.content(batch_status.output_file_id)
        
        # 创建结果文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        result_filename = f"batch_result_{KNOWLEDGE_BASE_ID}_{timestamp}.jsonl"
        result_filepath = os.path.join(os.getcwd(), result_filename)
        
        content.write_to_file(result_filepath)
        
        log_display.append(f"结果文件下载成功: {result_filepath}")
        return result_filepath
        
    except Exception as e:
        log_display.append(f"下载结果文件失败: {e}")
        return None


def parse_batch_results(result_file_path):
    """解析批量处理结果"""
    try:
        log_display = LogDisplay()
        results = {}
        
        with open(result_file_path, 'r', encoding='utf-8') as f:
            lins = f.readlines()
            lins = lins[:6000]
            for line in lins:
                if line.strip():
                    result = json.loads(line)
                    custom_id = result.get('custom_id', '')
                    result = result["response"]
                    if result.get('status_code') == 200:
                        # 提取数据ID
                        data_id = custom_id.split('-')[1] if '-' in custom_id else custom_id
                        
                        try:
                            # 解析AI响应
                            response_content = result['body']['choices'][0]['message']['content']
                            ai_result = json.loads(response_content)
                            
                            document_type = ai_result.get("document_type", "其他")
                            indexes = ai_result.get("indexes", [])
                            
                            # 验证和清理索引数据
                            valid_indexes = []
                            for index in indexes:
                                if isinstance(index, dict) and "type" in index and "text" in index:
                                    if index["text"] and index["text"].strip():
                                        valid_indexes.append({
                                            "type": index["type"],
                                            "text": index["text"].strip()
                                        })
                            
                            results[data_id] = {
                                "document_type": document_type,
                                "indexes": valid_indexes
                            }
                            
                        except Exception as e:
                            log_display.append(f'解析结果失败 {custom_id}: {e}')
                    else:
                        log_display.append(f'请求失败 {custom_id}: {result.get("error", "未知错误")}')
        
        log_display.append(f'成功解析 {len(results)} 个结果')
        return results
        
    except Exception as e:
        log_display.append(f'解析批量结果失败: {e}')
        return {}


def sync_indexes_to_database(batch_results):
    """同步索引到数据库"""
    try:
        log_display = LogDisplay()
        count = 0
        
        # 连接MongoDB
        mongo_client = MongoClient(MONGO_URI)
        db = mongo_client['fastgpt']
        
        # 获取集合信息
        dataids = [ObjectId(KNOWLEDGE_BASE_ID)]
        collections = db['dataset_collections'].find({"datasetId": {"$in": dataids}, "type": "file"})
        coll_dict = {
            str(col["_id"]): {
                "name": col["name"],
                "cpxh": col.get("metadata", {}).get("cpxh", ""),
                "cpmc": col.get("metadata", {}).get("cpmc", "")
            } for col in collections
        }
        
        log_display.append(f"开始同步 {len(batch_results)} 个索引到数据库...")
        
        for data_id, result in batch_results.items():
            try:
                # 查询原始数据
                original_data = db['dataset_datas'].find_one({"_id": ObjectId(data_id)})
                if not original_data:
                    log_display.append(f"未找到数据ID: {data_id}")
                    continue
                
                # 构建新的索引
                baseindexs = original_data.get('indexes', [])
                if len(baseindexs) != 1:
                    log_display.append(f"索引数量不为1: {data_id}")
                    continue
                newindexs = []
                for ii in baseindexs:
                    if ii.get('type', '') == 'default':
                        ii["_id"] = str(ii["_id"])
                        newindexs.append(ii)
                    else:
                        newindexs.append({"text": ii["text"]})
                
                # 添加AI生成的索引
                ai_indexes = result.get('indexes', [])
                for ai_index in ai_indexes:
                    ai_index_txt = ai_index["text"]
                    if ai_index["type"] == "文件名":
                        ai_index_txt = ai_index_txt.replace(".md", "").replace(".pdf", "").replace(".doc", "").replace(".docx", "")
                    newindexs.append({"text": ai_index_txt})
                
                # 记录文档类型到日志
                document_type = result.get('document_type', '其他')
                log_display.append(f'数据ID: {data_id}, 文档类型: {document_type}, 生成索引数量: {len(ai_indexes)}')
                
                # 更新数据库
                data = {
                    "dataId": str(original_data["_id"]),
                    "q": original_data['q'],
                    "a": original_data['a'],
                    "indexes": newindexs
                }
                
                response = requests.put(FASTGPT_API_URL, headers=FASTGPT_HEADERS, data=json.dumps(data))
                
                if response.status_code == 200:
                    log_display.append(f'数据ID {data_id} 更新成功')
                    count += 1
                else:
                    log_display.append(f'数据ID {data_id} 更新失败: {response.status_code}')
                
            except Exception as e:
                log_display.append(f'处理数据ID {data_id} 时发生错误: {e}')
        
        mongo_client.close()
        log_display.append(f'批量同步完成，成功更新 {count} 条记录')
        return count
        
    except Exception as e:
        log_display.append(f'同步索引到数据库失败: {e}')
        return 0


def main():
    """主函数"""
    log_display = LogDisplay()
    log_display.append("=" * 80)
    log_display.append(f"开始处理知识库 {KNOWLEDGE_BASE_ID} 的批量任务")
    log_display.append("=" * 80)
    
    # # 1. 持续查询批量任务状态直到完成
    # batch_status = check_batch_status_and_wait()
    # if not batch_status:
    #     log_display.append("批量任务未完成或查询失败，程序退出")
    #     return

    # # 2. 下载结果文件
    # result_file = download_batch_results(batch_status)
    # if not result_file:
    #     log_display.append("无法下载结果文件，程序退出")
    #     return
    
    # 3. 解析结果
    result_file = r"C:\Users\<USER>\Desktop\demo\batch_result_66d517aee6ac063d23b738e9_20250718_175103.jsonl"
    batch_results = parse_batch_results(result_file)
    if not batch_results:
        log_display.append("解析结果失败，程序退出")
        return
    
    # 4. 同步索引到数据库
    updated_count = sync_indexes_to_database(batch_results)
    
    log_display.append("=" * 80)
    log_display.append(f"处理完成！成功更新 {updated_count} 条记录")
    log_display.append(f"结果文件保存在: {result_file}")
    log_display.append("=" * 80)


if __name__ == '__main__':
    main()
