"""
数据库连接和基础操作模块
"""
from pymongo import MongoClient
from bson.objectid import ObjectId
from config import serverIP, dataids_list


def init_mongodb():
    """初始化MongoDB连接"""
    client = MongoClient(f"mongodb://myusername:mypassword@{serverIP}:27017/fastgpt?authSource=admin&directConnection=true")
    db = client['fastgpt']
    return db


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.db = init_mongodb()
    
    def get_datasets(self, dataset_ids=None):
        """获取数据集列表"""
        if dataset_ids is None:
            dataset_ids = dataids_list
        return list(self.db['datasets'].find({"_id": {"$in": dataset_ids}}))
    
    def get_dataset_collections(self, dataset_ids=None):
        """获取数据集合列表"""
        if dataset_ids is None:
            dataset_ids = dataids_list
        return list(self.db['dataset_collections'].find({"datasetId": {"$in": dataset_ids}}))
    
    def get_collection_by_id(self, collection_id):
        """根据ID获取集合"""
        return self.db['dataset_collections'].find_one({"_id": ObjectId(collection_id)})
    
    def update_collection(self, collection_id, update_data):
        """更新集合数据"""
        return self.db['dataset_collections'].update_one(
            {"_id": ObjectId(collection_id)},
            {"$set": update_data}
        )
    
    def get_dataset_datas(self, collection_id):
        """获取数据集数据"""
        return list(self.db['dataset_datas'].find(
            {"collectionId": ObjectId(collection_id)}
        ).sort("chunkIndex", 1))
    
    def insert_file_list(self, file_data):
        """插入文件清单"""
        return self.db["files_list_wh"].insert_one(file_data)
    
    def delete_file_list(self, collection_id):
        """删除文件清单"""
        return self.db["files_list_wh"].delete_one({"collectionId": collection_id})
    
    def check_training_exists(self, collection_id):
        """检查训练任务是否存在"""
        return self.db["dataset_trainings"].count_documents({"collectionId": ObjectId(collection_id)}) > 0


# 全局数据库管理器实例
db_manager = DatabaseManager()
