"""
页面路由
"""
import os
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from backend.services.dataset_service import dataset_service

router = APIRouter()


@router.get("/", response_class=HTMLResponse)
@router.get("/home/", response_class=HTMLResponse)
async def home(request: Request):
    """首页 - 显示知识库列表"""
    with open(os.path.join("templates", "home.html"), "r", encoding="utf-8") as file:
        html_content = file.read()
    return HTMLResponse(content=html_content, status_code=200)


@router.get("/index/", response_class=HTMLResponse)
@router.get("/docs/", response_class=HTMLResponse)
async def index(request: Request):
    """原有的文档库页面"""
    # 确保全局数据已加载
    if not dataset_service.global_datasets_dic or not dataset_service.global_datasets_collections_dic:
        dataset_service.get_global_data()
    
    with open(os.path.join("templates", "docs.html"), "r", encoding="utf-8") as file:
        html_content = file.read()
    return HTMLResponse(content=html_content, status_code=200)
