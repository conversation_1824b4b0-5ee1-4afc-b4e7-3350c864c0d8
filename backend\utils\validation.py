"""
验证工具模块
"""
from fastapi import UploadFile
from typing import Dict


async def validate_upload_file(file: UploadFile) -> Dict[str, any]:
    """
    验证上传文件的有效性
    """
    # 检查文件大小 (50MB限制)
    max_size = 50 * 1024 * 1024
    if file.size > max_size:
        return {
            "valid": False, 
            "message": f"文件大小超过限制 (最大50MB)，当前文件大小: {file.size / 1024 / 1024:.2f}MB"
        }

    # 检查文件类型
    allowed_extensions = ['.pdf', '.doc', '.docx', '.txt', '.md']
    file_ext = '.' + file.filename.split('.')[-1].lower() if '.' in file.filename else ''

    if file_ext not in allowed_extensions:
        return {
            "valid": False, 
            "message": f"不支持的文件类型: {file_ext}，支持的类型: {', '.join(allowed_extensions)}"
        }

    # 检查文件名
    if not file.filename or len(file.filename.strip()) == 0:
        return {"valid": False, "message": "文件名不能为空"}

    if len(file.filename) > 255:
        return {"valid": False, "message": "文件名过长 (最大255字符)"}

    return {"valid": True, "message": "文件验证通过"}
