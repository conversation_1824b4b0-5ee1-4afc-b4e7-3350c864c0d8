"""
认证服务模块
"""
from hashlib import md5
from fastapi import HTTP<PERSON>xception
from config import baseusername, basepassword
from backend.models.schemas import LoginRequest, APIResponse


class AuthService:
    """认证服务类"""
    
    @staticmethod
    def get_token(username: str, password: str) -> str:
        """生成用户token"""
        return md5((username + '_' + password).encode()).hexdigest()
    
    @staticmethod
    def verify_token(token: str) -> bool:
        """验证token是否有效"""
        expected_token = AuthService.get_token(baseusername, basepassword)
        return token == expected_token
    
    @staticmethod
    async def login(login_request: LoginRequest) -> APIResponse:
        """用户登录"""
        try:
            if (login_request.username == baseusername and 
                login_request.password == basepassword):
                token = AuthService.get_token(baseusername, basepassword)
                return APIResponse(
                    code=200, 
                    message="登录成功", 
                    data={"token": token}
                )
            else:
                return APIResponse(code=401, message="账号或密码错误")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


# 全局认证服务实例
auth_service = AuthService()
