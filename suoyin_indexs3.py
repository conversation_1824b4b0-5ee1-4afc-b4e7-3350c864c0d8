import json
import requests
import pandas as pd
from pymongo import MongoClient
from bson.objectid import ObjectId
from zhipuai import ZhipuAI
from openai import OpenAI
import sys
import datetime
import time
import os
import tempfile

client = ZhipuAI(api_key="d3e441ee4a536dd76c88a404ad8b05e4.t97wBPQIa5GuFT4y")
clientKiMi = OpenAI(api_key="sk-h0iAzauhzJuCRCjvzxDmkcNKQNxUe6J2bcbrfTyw4mMIpqgX",base_url="https://api.moonshot.cn/v1")
clientDeepSeek = OpenAI(api_key="***********************************",base_url="https://api.deepseek.com")

dataids_list = [ObjectId("66d517aee6ac063d23b738e9")]
def add_indexes(logger_obj, date_time_str=None, text_value=None, file_path=None, use_batch=False):
    try:
        logger_obj.log_display.append(f'开始添加索引...')
        count = 0
        if date_time_str:
            date_time_str = datetime.datetime.strptime(date_time_str, '%Y-%m-%d %H:%M:%S')
        if file_path:
            pd_data = pd.read_excel(file_path).values.tolist()
            pddids = [ObjectId(pfd[0]) for pfd in pd_data]
            pdd_dict = {}
            for pfd in pd_data:
                if str(pfd[0]) in pdd_dict:
                    pdd_dict[str(pfd[0])].append({"text":pfd[1]})
                else:
                    pdd_dict[str(pfd[0])] = [{"text":pfd[1]}]
        for dataidss in dataids_list:
            dataids = [dataidss]
            mongo_client = MongoClient("***************************************************************************************************")
            db = mongo_client['fastgpt']
            collections = db['dataset_collections'].find({"datasetId": {"$in": dataids}, "type": "file"})
            coll_dict = {str(col["_id"]):{"name":col["name"],"cpxh":col.get("metadata",{}).get("cpxh",""),"cpmc":col.get("metadata",{}).get("cpmc","")} for col in collections }
            tag = 0
            if date_time_str or 1:
                dataset_datas = db['dataset_datas'].find({"$and": [{"datasetId":{"$in":dataids}},{"$expr":{"$lte":[{"$size":"$indexes"},1]}},{"updateTime":{"$gte":date_time_str}}]})
                tag = 1
            elif text_value:
                colls = db['dataset_collections'].find({"datasetId": {"$in": dataids},"name": text_value, "type": "file"})
                daids = [col["_id"] for col in colls]
                dataset_datas = db['dataset_datas'].find({"collectionId": {"$in": daids},"$expr": {"$lte": [{"$size": "$indexes"}, 2]}})
                tag = 2
            elif file_path:
                dataset_datas = db['dataset_datas'].find({"_id": {"$in": pddids}})
                tag = 3
            else:
                dataset_datas = db['dataset_datas'].find({"datasetId": {"$in": dataids},"$expr": {"$lte": [{"$size": "$indexes"}, 2]}})
                tag = 4

            # 如果使用批量处理
            if use_batch and tag != 3:  # 导入文件模式不使用批量处理
                logger_obj.log_display.append('使用批量处理模式...')

                # 收集需要处理的数据
                batch_data_items = []
                data_items_map = {}

                for it in dataset_datas:
                    try:
                        coll_data = coll_dict.get(str(it['collectionId']))
                        if not coll_data or '.md' not in coll_data["name"]:
                            continue

                        name = "文件名为："+coll_data["name"]
                        if coll_data["cpxh"]:
                            name += "，产品型号为："+coll_data["cpxh"]
                        if coll_data["cpmc"]:
                            name += "，产品名称为："+coll_data["cpmc"]

                        content = """%s 中的片段内容为 %s"""%(name,it['q'])

                        data_item = {
                            'data_id': str(it["_id"]),
                            'content': content,
                            'original_data': it,
                            'coll_data': coll_data
                        }

                        batch_data_items.append(data_item)
                        data_items_map[str(it["_id"])] = data_item

                    except Exception as e:
                        logger_obj.log_display.append(f'准备批量数据时发生错误: {e}')

                if batch_data_items:
                    logger_obj.log_display.append(f'准备批量处理 {len(batch_data_items)} 个数据项')

                    # 创建批量文件
                    jsonl_file = create_batch_jsonl_file(batch_data_items, logger_obj)
                    if jsonl_file:
                        # 处理批量任务
                        result_file = process_batch_with_zhipu(jsonl_file, logger_obj)
                        if result_file:
                            # 解析结果
                            batch_results = parse_batch_results(result_file, data_items_map, logger_obj)

                            # 更新数据库
                            count += update_database_with_batch_results(batch_results, data_items_map, logger_obj)

                            # 保留文件，不自动删除
                            logger_obj.log_display.append(f'批量处理完成，文件已保存:')
                            logger_obj.log_display.append(f'  请求文件: {jsonl_file}')
                            logger_obj.log_display.append(f'  结果文件: {result_file}')

                mongo_client.close()
                continue  # 跳过单个处理模式

            for it in dataset_datas:
                try:
                    baseindexs = it.get('indexes',[])
                    newindexs = []
                    for ii in baseindexs:
                        if ii.get('type','') == 'default':
                            ii["_id"] = str(ii["_id"])
                            newindexs.append(ii)
                        else:
                            newindexs.append({"text": ii["text"]})
                    
                    if tag == 3: # 导入只做增量
                        newindexs.extend(pdd_dict[str(it["_id"])])
                    else:
                        coll_data = coll_dict.get(str(it['collectionId']))
                        if '.md' not in coll_data["name"]:
                            continue
                        name = "文件名为："+coll_data["name"]
                        if coll_data["cpxh"]:
                            name += "，产品型号为："+coll_data["cpxh"]
                        if coll_data["cpmc"]:
                            name += "，产品名称为："+coll_data["cpmc"]

                        content = """%s 中的片段内容为 %s"""%(name,it['q'])

                        # 调用新的sync_deepseek函数
                        document_type, ai_indexes = sync_deepseek(content)

                        if not ai_indexes:
                            # 如果AI没有生成任何索引，跳过这条数据
                            continue

                        # 添加AI生成的索引
                        for ai_index in ai_indexes:
                            ai_index_txt = ai_index["text"]
                            if ai_index["type"] == "文件名":
                                ai_index_txt = ai_index_txt.replace(".md","").replace(".pdf","").replace(".doc","").replace(".docx","")
                            newindexs.append({"text": ai_index_txt})

                        # 记录文档类型到日志
                        logger_obj.log_display.append(f'文档类型识别: {document_type}, 生成索引数量: {len(ai_indexes)}')

                    data = {"dataId": str(it["_id"]),"q": it['q'],"a": it['a'],"indexes": newindexs}
                    url = 'http://171.43.138.237:3000/api/core/dataset/data/update'
                    headers = {'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o','Content-Type': 'application/json'}
                    response = requests.put(url, headers=headers, data=json.dumps(data))
                    if response.status_code == 200:
                        print('更新成功')
                        count += 1
                    else:
                        print('更新失败')
                except Exception as e:
                    logger_obj.log_display.append(f'发生了一个错误1: {e}')
            mongo_client.close()
    except Exception as e:
        logger_obj.log_display.append(f'发生了一个错误2: {e}')
    return count


def sync_deepseek(content):
    """
    根据文档类型生成不同的索引结构
    返回: List[Dict] - 索引列表，每个索引包含type和text字段
    """
    try:
        system_prompt = """
            Role: 智能文档索引生成专家
            Background: 需要根据文档类型和内容生成精准的索引，以提高知识库检索效果。
            Profile: 你是一位专业的文档分析和索引生成专家，擅长识别文档类型并提取相应的结构化信息。

            Skills:
            1. 文档类型识别能力
            2. 标题层级提取能力
            3. 关键信息抽取能力
            4. 产品信息识别能力
            5. 能够结合当前文本内容生成3个用户可能提到的相关问题,生成的问题必须能够在原文本内容中找到答案。

            Goals: 根据文档类型生成相应的索引结构，确保索引与内容高度相关。

            文档类型识别规则:
            1. 彩页：文件名包含"彩页"、"宣传"、"介绍"、"产品介绍"等关键词
            2. 用户手册：文件名包含"手册"、"说明书"、"操作指南"、"用户指南"等关键词
            3. 入门指南：文件名包含"入门"、"指南"、"快速入门"、"新手"等关键词
            4. 安装说明：文件名包含"安装"、"尺寸"、"规格"、"安装指南"、"尺寸说明"等关键词
            5. 每个文档能够结合当前文本内容生成3个用户可能提到的相关问题
            6. 其他：无法明确分类的文档

            索引生成规则:
            - 彩页：产品型号、产品名称、二级标题、关键词
            - 用户手册：产品型号、文件名、二级标题、关键词（如果有三级标题需要加入三级标题）
            - 入门指南：产品型号、文件名、二级标题、关键词
            - 安装说明：产品型号、文件名
            - 每个文档内容生成问题1、问题2、问题3
            - 其他：关键词

            Constraints:
            1. 所有提取的信息必须与文档内容直接相关
            2. 产品型号必须准确提取，不能编造
            3. 标题必须是文档中实际存在的标题
            4. 关键词要简洁且具有代表性
            5. 如果某类信息在文档中不存在，则不要生成该类索引
            6. 文件名作为索引时，去除后缀名
            7. 问题不能重复，且能够在原内容中找到准确答案。

            Output Format:
            请严格按照以下JSON格式输出：
            {
                "document_type": "彩页|用户手册|入门指南|安装说明|其他",
                "indexes": [
                    {"type": "产品型号", "text": "具体的产品型号"},
                    {"type": "产品名称", "text": "具体的产品名称"},
                    {"type": "文件名", "text": "文件名称"},
                    {"type": "二级标题", "text": "文档中的二级标题"},
                    {"type": "三级标题", "text": "文档中的三级标题"},
                    {"type": "关键词", "text": "相关关键词"},
                    {"type": "问题", "text": "相关问题"}
                ]
            }

            Examples:
            输入：文件名为：ABC-123用户手册.md，产品型号为：ABC-123，产品名称为：智能控制器 中的片段内容为 第一章 设备概述 1.1 产品介绍 本产品是一款高性能智能控制器
            输出：
            {
                "document_type": "用户手册",
                "indexes": [
                    {"type": "产品型号", "text": "ABC-123"},
                    {"type": "产品名称", "text": "智能控制器"},
                    {"type": "文件名", "text": "ABC-123用户手册"},
                    {"type": "二级标题", "text": "产品介绍"},
                    {"type": "关键词", "text": "高性能智能控制器"},
                    {"type": "问题", "text": "智能控制器的性能如何?"},
                    {"type": "问题", "text": "ABC-123用户手册在哪里下载?"},
                    {"type": "问题", "text": "智能控制器的使用说明在哪里?"}
                ]
            }

            请分析以下内容并生成相应的索引：
        """

        completion = clientDeepSeek.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "system", "content": system_prompt},{"role": "user", "content": content}],
            response_format={"type": "json_object"}
        )

        result = json.loads(completion.choices[0].message.content)
        document_type = result.get("document_type", "其他")
        indexes = result.get("indexes", [])

        # 验证和清理索引数据
        valid_indexes = []
        for index in indexes:
            if isinstance(index, dict) and "type" in index and "text" in index:
                if index["text"] and index["text"].strip():  # 确保文本不为空
                    valid_indexes.append({
                        "type": index["type"],
                        "text": index["text"].strip()
                    })

        return document_type, valid_indexes

    except Exception as e:
        print(f"sync_deepseek发生错误: {e}")
        return "其他", []


def create_batch_jsonl_file(data_items, logger_obj):
    """
    创建批量处理的JSONL文件
    data_items: 包含需要处理的数据项列表
    返回: 文件路径
    """
    try:
        # 创建项目目录下的文件，使用时间戳命名
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"batch_request_{timestamp}.jsonl"
        filepath = os.path.join(os.getcwd(), filename)

        temp_file = open(filepath, 'w', encoding='utf-8')

        system_prompt = """
            Role: 智能文档索引生成专家
            Background: 需要根据文档类型和内容生成精准的索引，以提高知识库检索效果。
            Profile: 你是一位专业的文档分析和索引生成专家，擅长识别文档类型并提取相应的结构化信息。

            Skills:
            1. 文档类型识别能力
            2. 标题层级提取能力
            3. 关键信息抽取能力
            4. 产品信息识别能力
            5. 能够结合当前文本内容生成3个用户可能提到的相关问题,生成的问题必须能够在原文本内容中找到答案。

            Goals: 根据文档类型生成相应的索引结构，确保索引与内容高度相关。

            文档类型识别规则:
            1. 彩页：文件名包含"彩页"、"宣传"、"介绍"、"产品介绍"等关键词
            2. 用户手册：文件名包含"手册"、"说明书"、"操作指南"、"用户指南"等关键词
            3. 入门指南：文件名包含"入门"、"指南"、"快速入门"、"新手"等关键词
            4. 安装说明：文件名包含"安装"、"尺寸"、"规格"、"安装指南"、"尺寸说明"等关键词
            5. 每个文档能够结合当前文本内容生成3个用户可能提到的相关问题
            6. 其他：无法明确分类的文档

            索引生成规则:
            - 彩页：产品型号、产品名称、二级标题、关键词
            - 用户手册：产品型号、文件名、二级标题、关键词（如果有三级标题需要加入三级标题）
            - 入门指南：产品型号、文件名、二级标题、关键词
            - 安装说明：产品型号、文件名
            - 每个文档内容生成问题1、问题2、问题3
            - 其他：关键词

            Constraints:
            1. 所有提取的信息必须与文档内容直接相关
            2. 产品型号必须准确提取，不能编造
            3. 标题必须是文档中实际存在的标题
            4. 关键词要简洁且具有代表性
            5. 如果某类信息在文档中不存在，则不要生成该类索引
            6. 文件名作为索引时，去除后缀名
            7. 问题不能重复，且能够在原内容中找到准确答案。

            Output Format:
            请严格按照以下JSON格式输出：
            {
                "document_type": "彩页|用户手册|入门指南|安装说明|其他",
                "indexes": [
                    {"type": "产品型号", "text": "具体的产品型号"},
                    {"type": "产品名称", "text": "具体的产品名称"},
                    {"type": "文件名", "text": "文件名称"},
                    {"type": "二级标题", "text": "文档中的二级标题"},
                    {"type": "三级标题", "text": "文档中的三级标题"},
                    {"type": "关键词", "text": "相关关键词"},
                    {"type": "问题", "text": "相关问题"}
                ]
            }

            请分析以下内容并生成相应的索引：
        """

        for i, item in enumerate(data_items):
            batch_request = {
                "custom_id": f"request-{item['data_id']}-{i}",
                "method": "POST",
                "url": "/v4/chat/completions",
                "body": {
                    "model": "GLM-4-Air-250414",
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": item['content']}
                    ],
                    "response_format": {"type": "json_object"},
                    "max_tokens": 4000
                }
            }
            temp_file.write(json.dumps(batch_request, ensure_ascii=False) + '\n')

        temp_file.close()
        logger_obj.log_display.append(f'批量文件创建成功: {filepath}，包含 {len(data_items)} 个请求')
        return filepath

    except Exception as e:
        logger_obj.log_display.append(f'创建批量文件失败: {e}')
        return None


def process_batch_with_zhipu(jsonl_file_path, logger_obj):
    """
    使用智普AI批量API处理请求
    """
    try:
        # 1. 上传批量文件
        logger_obj.log_display.append('正在上传批量文件...')
        with open(jsonl_file_path, 'rb') as f:
            file_result = client.files.create(
                file=f,
                purpose="batch"
            )

        file_id = file_result.id
        logger_obj.log_display.append(f'文件上传成功，文件ID: {file_id}')

        # 2. 创建批量任务
        logger_obj.log_display.append('正在创建批量任务...')
        batch_result = client.batches.create(
            input_file_id=file_id,
            endpoint="/v4/chat/completions",
            completion_window="24h",
            metadata={
                "description": "索引生成批量任务"
            }
        )

        batch_id = batch_result.id
        logger_obj.log_display.append(f'批量任务创建成功，任务ID: {batch_id}')

        # 3. 等待批量任务完成
        logger_obj.log_display.append('正在等待批量任务完成...')
        max_wait_time = 24 * 60 * 60  # 24小时
        check_interval = 30  # 30秒检查一次
        waited_time = 0

        while waited_time < max_wait_time:
            batch_status = client.batches.retrieve(batch_id)
            status = batch_status.status

            logger_obj.log_display.append(f'当前状态: {status}')

            if status == "completed":
                logger_obj.log_display.append('批量任务完成！')

                # 4. 下载结果文件
                if batch_status.output_file_id:
                    logger_obj.log_display.append('正在下载结果文件...')
                    content = client.files.content(batch_status.output_file_id)

                    # 创建项目目录下的结果文件
                    import datetime
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    result_filename = f"batch_result_{timestamp}.jsonl"
                    result_filepath = os.path.join(os.getcwd(), result_filename)

                    content.write_to_file(result_filepath)

                    logger_obj.log_display.append(f'结果文件下载成功: {result_filepath}')
                    return result_filepath
                else:
                    logger_obj.log_display.append('没有找到输出文件')
                    return None

            elif status in ["failed", "expired", "cancelled"]:
                logger_obj.log_display.append(f'批量任务失败，状态: {status}')
                return None

            # 等待一段时间后再次检查
            time.sleep(check_interval)
            waited_time += check_interval

            # 每5分钟显示一次进度
            if waited_time % 300 == 0:
                logger_obj.log_display.append(f'已等待 {waited_time // 60} 分钟...')

        logger_obj.log_display.append('等待超时，批量任务可能仍在处理中')
        return None

    except Exception as e:
        logger_obj.log_display.append(f'批量处理失败: {e}')
        return None


def parse_batch_results(result_file_path, data_items_map, logger_obj):
    """
    解析批量处理结果
    """
    try:
        results = {}

        with open(result_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    result = json.loads(line)
                    custom_id = result.get('custom_id', '')

                    if result.get('status_code') == 200:
                        # 提取数据ID
                        data_id = custom_id.split('-')[1] if '-' in custom_id else custom_id

                        try:
                            # 解析AI响应
                            response_content = result['body']['choices'][0]['message']['content']
                            ai_result = json.loads(response_content)

                            document_type = ai_result.get("document_type", "其他")
                            indexes = ai_result.get("indexes", [])

                            # 验证和清理索引数据
                            valid_indexes = []
                            for index in indexes:
                                if isinstance(index, dict) and "type" in index and "text" in index:
                                    if index["text"] and index["text"].strip():
                                        valid_indexes.append({
                                            "type": index["type"],
                                            "text": index["text"].strip()
                                        })

                            results[data_id] = {
                                "document_type": document_type,
                                "indexes": valid_indexes
                            }

                        except Exception as e:
                            logger_obj.log_display.append(f'解析结果失败 {custom_id}: {e}')
                    else:
                        logger_obj.log_display.append(f'请求失败 {custom_id}: {result.get("error", "未知错误")}')

        logger_obj.log_display.append(f'成功解析 {len(results)} 个结果')
        return results

    except Exception as e:
        logger_obj.log_display.append(f'解析批量结果失败: {e}')
        return {}


def update_database_with_batch_results(batch_results, data_items_map, logger_obj):
    """
    使用批量处理结果更新数据库
    """
    count = 0
    try:
        for data_id, result in batch_results.items():
            try:
                if data_id not in data_items_map:
                    continue

                data_item = data_items_map[data_id]
                it = data_item['original_data']

                # 构建新的索引
                baseindexs = it.get('indexes', [])
                newindexs = []
                for ii in baseindexs:
                    if ii.get('type', '') == 'default':
                        ii["_id"] = str(ii["_id"])
                        newindexs.append(ii)
                    else:
                        newindexs.append({"text": ii["text"]})

                # 添加AI生成的索引
                ai_indexes = result.get('indexes', [])
                for ai_index in ai_indexes:
                    ai_index_txt = ai_index["text"]
                    if ai_index["type"] == "文件名":
                        ai_index_txt = ai_index_txt.replace(".md","").replace(".pdf","").replace(".doc","").replace(".docx","")
                    newindexs.append({"text": ai_index_txt})

                # 记录文档类型到日志
                document_type = result.get('document_type', '其他')
                logger_obj.log_display.append(f'文档类型识别: {document_type}, 生成索引数量: {len(ai_indexes)}')

                # 更新数据库
                data = {
                    "dataId": str(it["_id"]),
                    "q": it['q'],
                    "a": it['a'],
                    "indexes": newindexs
                }

                url = 'http://171.43.138.237:3000/api/core/dataset/data/update'
                headers = {
                    'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o',
                    'Content-Type': 'application/json'
                }
                response = requests.put(url, headers=headers, data=json.dumps(data))

                if response.status_code == 200:
                    print('更新成功')
                    count += 1
                else:
                    print('更新失败')

            except Exception as e:
                logger_obj.log_display.append(f'更新数据库时发生错误: {e}')

        logger_obj.log_display.append(f'批量更新完成，成功更新 {count} 条记录')
        return count

    except Exception as e:
        logger_obj.log_display.append(f'批量更新数据库失败: {e}')
        return 0


import argparse
import os


class LogDisplay:
    """简单的日志显示类，用于替代PyQt5的日志显示功能"""

    def append(self, message):
        """打印日志消息"""
        print(f"[LOG] {message}")


def get_user_input():
    """获取用户输入的参数"""
    print("=" * 60)
    print("索引生成工具 (支持智普AI批量处理)")
    print("=" * 60)
    print("\n提示：")
    print("1. 生成索引条件必须选择一个")
    print("2. 选择时间后将生成这个时间之后新加的数据生成索引")
    print("3. 输入文件全称将只处理这一个文件里面的索引")
    print("4. 导入文件将根据提供的导入模版里面的知识片段id更新索引")
    print("5. 文件格式仅支持xlsx，第一列为知识片段id，第二列为索引")
    print("6. 当填写多个条件时，优先级：选择时间 > 文件名称 > 导入文件")
    print("7. 执行之前先向管理员申请ip白名单！")
    print("8. 批量处理模式：使用智普AI批量API，适合大量数据处理，成本更低")
    print("-" * 60)

    # 获取日期时间
    date_time_str = input("\n请输入时间 (格式: YYYY-MM-DD HH:MM:SS，留空则不使用时间条件): ").strip()
    if not date_time_str:
        date_time_str = None

    # 获取文件名称
    text_value = input("请输入文件名称 (留空则不使用文件名条件): ").strip()
    if not text_value:
        text_value = None

    # 获取文件路径
    file_path = input("请输入Excel文件路径 (留空则不使用文件导入): ").strip()
    if file_path and not os.path.exists(file_path):
        print(f"警告: 文件 {file_path} 不存在！")
        file_path = None
    elif not file_path:
        file_path = None

    # 获取批量处理选项
    use_batch_input = input("是否使用批量处理模式？(y/n，默认n): ").strip().lower()
    use_batch = use_batch_input in ['y', 'yes', '是', '1', 'true']

    return date_time_str, text_value, file_path, use_batch


def execute_add_indexes():
    """执行索引生成的主函数"""
    # 获取用户输入
    date_time_str, text_value, file_path, use_batch = get_user_input()

    # 验证至少有一个条件
    if not date_time_str and not text_value and not file_path:
        print("\n错误: 请至少选择一个条件！")
        return

    # 创建日志显示对象
    log_display = LogDisplay()

    print(f"\n开始执行索引生成...")
    print(f"日期时间: {date_time_str or '未设置'}")
    print(f"文件名称: {text_value or '未设置'}")
    print(f"文件路径: {file_path or '未设置'}")
    print(f"批量处理: {'是' if use_batch else '否'}")
    print("-" * 60)

    # 调用 add_indexes 方法
    try:
        # 创建一个简单的对象来模拟self
        class SimpleObject:
            def __init__(self):
                self.log_display = log_display

        obj = SimpleObject()
        count = add_indexes(obj, date_time_str, text_value, file_path, use_batch)
        print(f"\n✅ 成功！{count}个知识片段的索引生成完成！")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")


def main_with_args():
    """使用命令行参数的主函数"""
    parser = argparse.ArgumentParser(description='索引生成工具 (支持智普AI批量处理)')
    parser.add_argument('--datetime', '-d', help='时间条件 (格式: YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--filename', '-f', help='文件名称条件')
    parser.add_argument('--filepath', '-p', help='Excel文件路径')
    parser.add_argument('--batch', '-b', action='store_true', help='使用批量处理模式')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式模式')

    args = parser.parse_args()

    if args.interactive:
        execute_add_indexes()
        return

    # 验证至少有一个条件
    if not args.datetime and not args.filename and not args.filepath:
        print("错误: 请至少提供一个条件参数，或使用 -i 进入交互式模式")
        parser.print_help()
        return

    # 验证文件路径
    if args.filepath and not os.path.exists(args.filepath):
        print(f"错误: 文件 {args.filepath} 不存在！")
        return

    # 创建日志显示对象
    log_display = LogDisplay()

    print("开始执行索引生成...")
    print(f"日期时间: {args.datetime or '未设置'}")
    print(f"文件名称: {args.filename or '未设置'}")
    print(f"文件路径: {args.filepath or '未设置'}")
    print(f"批量处理: {'是' if args.batch else '否'}")
    print("-" * 60)

    try:
        # 创建一个简单的对象来模拟self
        class SimpleObject:
            def __init__(self):
                self.log_display = log_display

        obj = SimpleObject()
        count = add_indexes(obj, args.datetime, args.filename, args.filepath, args.batch)
        print(f"\n✅ 成功！{count}个知识片段的索引生成完成！")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")




# def update_indexes_tem():
#     try:
#         count = 0
#         for dataidss in dataids_list:
#             dataids = [dataidss]
#             client = MongoClient("***************************************************************************************************")
#             db = client['fastgpt']
#             collections = db['dataset_collections'].find({"datasetId": {"$in": dataids}, "type": "file"})
#             coll_dict = {str(col["_id"]):{"name":col["name"],"cpxh":col.get("metadata",{}).get("cpxh",""),"cpmc":col.get("metadata",{}).get("cpmc","")} for col in collections }
#             dataset_datas = db['dataset_datas'].find({"datasetId": {"$in": dataids},"$expr": {"$lte": [{"$size": "$indexes"}, 2]}})
#             for it in dataset_datas:
#                 try:
#                     coll_data = coll_dict.get(str(it['collectionId']))
#                     name = "文件名为："+coll_data["name"]
#                     if coll_data["cpxh"]:
#                         name += "，产品型号为："+coll_data["cpxh"]
#                     if coll_data["cpmc"]:
#                         name += "，产品名称为："+coll_data["cpmc"]
                        
#                     content = """%s 中的片段内容为 %s"""%(name,it['q'])
#                     gjzsd,gjc = sync_deepseek(content)
#                     if not gjzsd and not gjc:
#                         continue
                    
#                     baseindexs = it.get('indexes',[])
#                     newindexs = []
#                     for ii in baseindexs:
#                         if ii.get('defaultIndex',False) == True:
#                             ii["_id"] = str(ii["_id"])
#                             newindexs.append(ii)
#                         else:
#                             newindexs.append({"text": ii["text"]})
#                     newindexs.append({"text": gjzsd})
#                     newindexs.append({"text": gjc})
#                     if coll_data["cpxh"] and coll_data["cpmc"]:
#                         newindexs.append({"text": f"{coll_data['cpxh']}{coll_data['cpmc']}"})

#                     data = {"dataId": str(it["_id"]),"q": it['q'],"a": it['a'],"indexes": newindexs}
#                     url = 'http://171.43.138.237:3000/api/core/dataset/data/update'
#                     headers = {'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o','Content-Type': 'application/json'}
#                     response = requests.put(url, headers=headers, data=json.dumps(data))
#                     if response.status_code == 200:
#                         print('更新成功')
#                         count += 1
#                     else:
#                         print('更新失败')

#                 except Exception as e:
#                     print(f"发生了一个错误1: {e}")
#             client.close()
#         print("修改成功数量",count)
#     except Exception as e:
#         print(f"发生了一个错误2: {e}")


if __name__ == '__main__':
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 使用命令行参数模式
        main_with_args()
    else:
        # 使用交互式模式
        execute_add_indexes()
    # update_indexes_tem()