<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>文档库管理 - 知识库管理系统</title>
  <!-- 引入Element UI样式 -->
  <link rel="stylesheet" href="/static/css/index.css">
  <!-- 引入统一样式 -->
  <link rel="stylesheet" href="/static/css/common.css">
  <!-- 引入Vue.js -->
  <script src="/static/js/vue.js"></script>
  <!-- 引入Element UI组件库 -->
  <script src="/static/js/index.js"></script>
  <!-- 引入axios -->
  <script src="/static/js/axios.min.js"></script>
  <!-- 引入marked.js用于Markdown渲染 -->
  <script src="/static/js/marked.min.js"></script>

  <style>
    /* 页面特定样式 */
    .docs-header {
      background: var(--primary-gradient);
      color: white;
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 8px;
    }
    
    .docs-header h2 {
      margin: 0 0 10px 0;
      font-size: 24px;
      font-weight: 600;
    }
    
    .docs-header p {
      margin: 0;
      opacity: 0.9;
      font-size: 14px;
    }
    
    .breadcrumb {
      margin-bottom: 20px;
    }
    
    .back-btn {
      background: var(--background-white);
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
      padding: 8px 16px;
      border-radius: 6px;
      text-decoration: none;
      font-size: 14px;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
    
    .back-btn:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }
    
    .stats-bar {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      padding: 16px;
      background: var(--background-white);
      border-radius: 8px;
      box-shadow: var(--shadow-light);
    }
    
    .stat-item {
      text-align: center;
      flex: 1;
    }
    
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary-color);
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .filter-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 16px;
      background: var(--background-white);
      border-radius: 8px;
      box-shadow: var(--shadow-light);
    }
    
    .filter-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }
    
    .filter-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
    
    .search-box {
      width: 300px;
    }
    
    /* 文件卡片网格布局 */
    .files-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }
    
    .file-card-enhanced {
      background: var(--background-white);
      border-radius: 12px;
      padding: 20px;
      box-shadow: var(--shadow-light);
      transition: all 0.3s ease;
      position: relative;
      border: 1px solid var(--border-lighter);
    }
    
    .file-card-enhanced:hover {
      box-shadow: var(--shadow-hover);
      transform: translateY(-4px);
      border-color: var(--primary-color);
    }
    
    .file-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
    }
    
    .file-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--primary-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
      margin-right: 12px;
    }
    
    .file-actions {
      display: flex;
      gap: 8px;
    }
    
    .action-btn {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      border: none;
      background: var(--background-base);
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .action-btn:hover {
      background: var(--primary-color);
      color: white;
    }
    
    .file-content {
      flex: 1;
    }
    
    .file-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 8px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .file-description {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 12px;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .file-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid var(--border-extra-light);
    }
    
    .file-meta-info {
      font-size: 12px;
      color: var(--text-placeholder);
    }
    
    .status-indicator {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-pending {
      background: #fef0f0;
      color: var(--danger-color);
    }
    
    .status-approved {
      background: #f0f9ff;
      color: var(--success-color);
    }
    
    /* 空状态样式 */
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: var(--text-secondary);
    }
    
    .empty-state i {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.6;
    }
    
    .empty-state h3 {
      font-size: 18px;
      margin-bottom: 10px;
      color: var(--text-primary);
    }
    
    .empty-state p {
      font-size: 14px;
      opacity: 0.8;
    }
    
    /* 上传对话框样式 */
    .upload-dialog .el-dialog__body {
      padding: 20px 30px;
    }

    .upload-step {
      min-height: 400px;
    }

    .step-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .step-header h3 {
      margin: 0 0 10px 0;
      color: var(--text-primary);
      font-size: 20px;
    }

    .step-header p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 14px;
    }

    /* 文件上传区域 */
    .upload-area {
      border: 2px dashed var(--border-base);
      border-radius: 12px;
      padding: 40px 20px;
      text-align: center;
      transition: all 0.3s ease;
      background: var(--background-base);
      margin-bottom: 20px;
    }

    .upload-area:hover,
    .upload-area.drag-over {
      border-color: var(--primary-color);
      background: #f0f4ff;
    }

    .upload-icon {
      font-size: 48px;
      color: var(--primary-color);
      margin-bottom: 20px;
    }

    .upload-title {
      font-size: 16px;
      color: var(--text-primary);
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    .upload-subtitle {
      font-size: 14px;
      color: var(--text-secondary);
      margin: 0 0 20px 0;
    }

    /* 已选择文件列表 */
    .selected-files {
      margin-top: 20px;
    }

    .selected-files h4 {
      margin: 0 0 15px 0;
      color: var(--text-primary);
      font-size: 16px;
    }

    .file-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid var(--border-lighter);
      border-radius: 8px;
      margin-bottom: 8px;
      background: var(--background-white);
      transition: all 0.3s ease;
    }

    .file-item:hover {
      border-color: var(--primary-color);
      box-shadow: var(--shadow-light);
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .file-icon {
      font-size: 24px;
      color: var(--primary-color);
      margin-right: 12px;
    }

    .file-details {
      flex: 1;
    }

    .file-name {
      font-size: 14px;
      color: var(--text-primary);
      font-weight: 500;
      margin-bottom: 4px;
    }

    .file-meta {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .remove-btn {
      color: var(--danger-color);
      padding: 4px;
    }

    .remove-btn:hover {
      background: #fef0f0;
    }

    /* 文件信息表单 */
    .file-info-forms {
      max-height: 400px;
      overflow-y: auto;
    }

    .file-info-form {
      border: 1px solid var(--border-lighter);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      background: var(--background-white);
    }

    .form-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-extra-light);
    }

    .form-header i {
      font-size: 20px;
      color: var(--primary-color);
      margin-right: 8px;
    }

    .form-header span {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }

    .info-form .el-form-item {
      margin-bottom: 18px;
    }

    /* 信息提取加载状态 */
    .extracting-info {
      text-align: center;
      padding: 60px 20px;
      color: var(--text-secondary);
    }

    .extracting-info p {
      margin-top: 16px;
      font-size: 14px;
    }

    /* 上传设置表单 */
    .settings-form {
      max-width: 600px;
      margin: 0 auto;
    }

    .settings-form .el-form-item {
      margin-bottom: 24px;
    }

    /* 上传进度 */
    .upload-progress {
      max-height: 400px;
      overflow-y: auto;
    }

    .progress-item {
      margin-bottom: 20px;
      padding: 16px;
      border: 1px solid var(--border-lighter);
      border-radius: 8px;
      background: var(--background-white);
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .progress-header .file-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
    }

    .progress-status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .progress-status.uploading {
      background: #e6f7ff;
      color: var(--primary-color);
    }

    .progress-status.success {
      background: #f6ffed;
      color: var(--success-color);
    }

    .progress-status.error {
      background: #fff2f0;
      color: var(--danger-color);
    }

    .error-message {
      margin-top: 8px;
      font-size: 12px;
      color: var(--danger-color);
      background: #fff2f0;
      padding: 8px;
      border-radius: 4px;
    }

    .overall-progress {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid var(--border-extra-light);
    }

    .overall-progress h4 {
      margin: 0 0 15px 0;
      color: var(--text-primary);
      font-size: 16px;
    }

    /* 批量选择样式 */
    .batch-select-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: var(--background-white);
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: var(--shadow-light);
      border: 1px solid var(--border-lighter);
    }

    .select-info {
      font-size: 14px;
      color: var(--text-secondary);
    }

    .file-checkbox {
      position: absolute;
      top: 12px;
      left: 12px;
      z-index: 10;
    }

    .file-card-enhanced {
      position: relative;
      padding-left: 50px; /* 为复选框留出空间 */
    }

    .file-card-enhanced.no-checkbox {
      padding-left: 20px; /* 没有复选框时的正常边距 */
    }

    /* 预览抽屉样式 */
    .preview-drawer .el-drawer__body {
      padding: 20px;
    }

    .preview-card-container {
      width: 100%;
      margin-bottom: 20px;
    }

    .preview-card {
      position: relative;
      border: 1px solid var(--border-base);
      border-radius: 8px;
      transition: all 0.3s ease;
      padding: 16px;
      background-color: #f0f4ff;
    }

    .preview-card:hover {
      box-shadow: var(--shadow-base);
      transform: translateY(-2px);
      border-color: var(--primary-color);
      background-color: #e8ecff;
    }

    .preview-card img {
      width: 100%;
      height: auto;
      object-fit: contain;
      border-radius: 4px;
    }

    .preview-id {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 12px;
      color: white;
      background: rgba(0, 0, 0, 0.6);
      padding: 2px 6px;
      border-radius: 4px;
    }

    .preview-delete {
      position: absolute;
      bottom: 10px;
      right: 10px;
      cursor: pointer;
      color: var(--danger-color);
      font-size: 18px;
      transition: color 0.3s ease;
    }

    .preview-delete:hover {
      color: #ff0000;
    }

    /* 预览模态框样式 */
    .preview-modal .el-dialog__body {
      padding: 20px;
    }

    .preview-modal .el-tabs__content {
      padding: 20px 0;
    }

    .index-item {
      margin-bottom: 15px;
    }

    .default-index, .custom-index {
      border: 1px solid var(--border-base);
      border-radius: 8px;
      padding: 12px;
      background-color: var(--background-base);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .custom-index:hover {
      box-shadow: var(--shadow-base);
      border-color: var(--primary-color);
      background-color: #e8ecff;
    }

    .index-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    /* 审核对话框样式 */
    .audit-dialog .el-dialog__body {
      padding: 20px 30px;
    }

    .audit-dialog-content {
      min-height: 200px;
    }

    .audit-dialog .el-form-item {
      margin-bottom: 20px;
    }

    .audit-dialog .el-radio-group {
      display: flex;
      gap: 20px;
    }

    /* 动画和过渡效果 */
    .files-grid {
      transition: all 0.3s ease;
    }

    .file-card-enhanced {
      transition: all 0.3s ease;
    }

    .file-card-enhanced:hover {
      transform: translateY(-2px);
    }

    .action-btn {
      transition: all 0.2s ease;
    }

    .action-btn:hover {
      transform: scale(1.1);
    }

    /* 预览抽屉动画 */
    .el-drawer__body {
      transition: all 0.3s ease;
    }

    /* 对话框动画 */
    .el-dialog {
      transition: all 0.3s ease;
    }

    /* 权限提示样式 */
    .permission-hint {
      color: var(--text-placeholder);
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      background: var(--background-base);
      cursor: not-allowed;
    }

    .permission-hint:hover {
      background: var(--border-lighter);
    }

    .file-card-enhanced.selected {
      border-color: var(--primary-color);
      background: #f0f4ff;
    }

    .file-meta-details {
      margin-top: 8px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .meta-tag {
      font-size: 12px;
      padding: 2px 6px;
      background: var(--background-base);
      color: var(--text-secondary);
      border-radius: 4px;
      border: 1px solid var(--border-extra-light);
    }

    /* 审核按钮样式 */
    .approve-btn {
      color: var(--success-color);
    }

    .approve-btn:hover {
      background: #f0f9ff;
      color: var(--success-color);
    }

    .reject-btn {
      color: var(--danger-color);
    }

    .reject-btn:hover {
      background: #fff2f0;
      color: var(--danger-color);
    }

    /* 状态指示器扩展 */
    .status-rejected {
      background: #fff2f0;
      color: var(--danger-color);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .files-grid {
        grid-template-columns: 1fr;
      }

      .filter-bar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .filter-left, .filter-right {
        justify-content: center;
      }

      .search-box {
        width: 100%;
      }

      .stats-bar {
        flex-direction: column;
        gap: 12px;
      }

      .stat-item {
        padding: 8px;
        background: var(--background-base);
        border-radius: 6px;
      }

      .upload-dialog {
        width: 95% !important;
      }

      .upload-area {
        padding: 30px 15px;
      }

      .upload-icon {
        font-size: 36px;
      }

      .file-info-form {
        padding: 15px;
      }

      .settings-form {
        max-width: 100%;
      }

      /* 预览抽屉在移动端全屏显示 */
      .preview-drawer .el-drawer {
        width: 100% !important;
      }

      /* 审核对话框在移动端优化 */
      .audit-dialog {
        width: 95% !important;
      }

      /* 预览模态框在移动端优化 */
      .preview-modal {
        width: 95% !important;
      }

      /* 登录对话框在移动端优化 */
      .el-dialog {
        width: 95% !important;
        margin: 0 auto;
      }

      /* 文件操作按钮在移动端优化 */
      .file-actions {
        flex-wrap: wrap;
        gap: 4px;
      }

      .action-btn {
        min-width: 32px;
        min-height: 32px;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="main-container">
      <!-- 页面头部 -->
      <div class="docs-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <h2>{{ currentDataset.name || '文档库管理' }}</h2>
            <p>{{ currentDataset.intro || '管理和审核知识库文档' }}</p>
          </div>
          <a href="/" class="back-btn">
            <i class="el-icon-arrow-left"></i>
            返回首页
          </a>
        </div>
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item><a href="/">首页</a></el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentDataset.name || '文档库' }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 统计信息栏 -->
      <div class="stats-bar">
        <div class="stat-item">
          <div class="stat-number">{{ stats.totalDocs }}</div>
          <div class="stat-label">文档总数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ stats.auditedDocs }}</div>
          <div class="stat-label">已审核</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ stats.pendingDocs }}</div>
          <div class="stat-label">待审核</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ stats.auditRate }}%</div>
          <div class="stat-label">审核率</div>
        </div>
      </div>

      <!-- 筛选和搜索栏 -->
      <div class="filter-bar">
        <div class="filter-left">
          <el-select v-model="filterStatus" placeholder="筛选状态" style="width: 120px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="已审核" value="1"></el-option>
            <el-option label="待审核" value="0"></el-option>
          </el-select>
          
          <el-select v-model="selectedDataset" placeholder="选择知识库" style="width: 200px;" @change="onDatasetChange">
            <el-option 
              v-for="dataset in datasets" 
              :key="dataset.id" 
              :label="dataset.name" 
              :value="dataset.id">
            </el-option>
          </el-select>
        </div>
        
        <div class="filter-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文档名称..."
            class="search-box"
            prefix-icon="el-icon-search"
            @input="onSearch">
          </el-input>

          <!-- 批量操作按钮 - 需要登录 -->
          <el-dropdown v-if="isLoggedIn && selectedFiles.length > 0" @command="handleBatchAction">
            <el-button type="warning">
              批量操作 ({{ selectedFiles.length }}) <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="batchApprove">批量审核通过</el-dropdown-item>
              <el-dropdown-item command="batchReject">批量审核拒绝</el-dropdown-item>
              <el-dropdown-item command="batchDelete" divided>批量删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-button v-if="isLoggedIn" type="primary" icon="el-icon-upload" @click="showUploadDialog">
            上传文档
          </el-button>

          <!-- 登录/退出按钮 -->
          <el-button v-if="!isLoggedIn"
            type="info"
            icon="el-icon-user"
            @click="showLoginDialog">
            登录
          </el-button>
          <el-button v-else
            type="primary"
            icon="el-icon-user"
            @click="handleLogout">
            退出
          </el-button>
        </div>
      </div>

      <!-- 文档列表 -->
      <div v-if="loading" class="loading-container">
        <i class="el-icon-loading" style="font-size: 32px; margin-bottom: 16px;"></i>
        <p style="margin-top: 16px; color: var(--text-secondary);">正在加载文档列表...</p>
      </div>

      <div v-else-if="filteredFiles.length > 0">
        <!-- 全选控制 - 需要登录 -->
        <div class="batch-select-bar" v-if="isLoggedIn && filteredFiles.length > 0">
          <el-checkbox
            v-model="selectAll"
            @change="handleSelectAll"
            :indeterminate="isIndeterminate">
            全选 ({{ selectedFileIds.length }}/{{ filteredFiles.length }})
          </el-checkbox>
          <span class="select-info">
            已选择 {{ selectedFileIds.length }} 个文档
          </span>
        </div>

        <div class="files-grid">
          <div
            v-for="file in filteredFiles"
            :key="file.id"
            class="file-card-enhanced"
            :class="{
              'selected': selectedFileIds.includes(file.id),
              'no-checkbox': !isLoggedIn
            }"
            @click="viewFile(file)"
          >
            <!-- 选择框 - 需要登录 -->
            <div v-if="isLoggedIn" class="file-checkbox" @click.stop>
              <el-checkbox
                v-model="selectedFileIds"
                :label="file.id">
              </el-checkbox>
            </div>

            <div class="file-header">
              <div style="display: flex; align-items: center; flex: 1;margin-top:24px">
                <div class="file-content">
                  <div class="file-title">{{ file.name }}</div>
                  <!-- 文件详细信息 -->
                  <div class="file-meta-details" v-if="file.cpxh || file.size">
                    <span v-if="file.cpxh" class="meta-tag">型号: {{ file.cpxh }}</span>
                    <span v-if="file.size" class="meta-tag">大小: {{ formatFileSize(file.size) }}</span>
                  </div>
                </div>
              </div>

              <div class="file-actions">
                <!-- 预览按钮 - 所有用户都可以使用 -->
                <button class="action-btn" @click.stop="viewFile(file)" title="预览内容">
                  <i class="el-icon-document"></i>
                </button>

                <!-- 管理员功能 - 需要登录 -->
                <template v-if="isLoggedIn">
                  <button
                    v-if="file.audit === '0'"
                    class="action-btn approve-btn"
                    @click.stop="approveFile(file)"
                    title="审核通过">
                    <i class="el-icon-check"></i>
                  </button>
                  <button
                    v-if="file.audit === '0'"
                    class="action-btn reject-btn"
                    @click.stop="rejectFile(file)"
                    title="审核拒绝">
                    <i class="el-icon-close"></i>
                  </button>
                  <button class="action-btn" @click.stop="editFile(file)" title="编辑文档">
                    <i class="el-icon-edit"></i>
                  </button>
                  <button class="action-btn" @click.stop="deleteFile(file)" title="删除文档">
                    <i class="el-icon-delete"></i>
                  </button>
                </template>
                <!-- 未登录用户提示 -->
                <template v-else>
                  <span class="permission-hint" title="需要管理员权限">
                    <i class="el-icon-lock"></i>
                  </span>
                </template>
              </div>
            </div>

            <div class="file-footer">
              <div class="file-meta-info">
                {{ formatDate(file.updateTime) }}
              </div>
              <div
                :class="['status-indicator', getStatusClass(file.audit)]"
              >
                {{ getStatusText(file.audit) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <h3>暂无文档</h3>
        <p v-if="isLoggedIn">还没有上传任何文档，点击上传按钮开始添加文档</p>
        <p v-else>当前知识库暂无文档，请联系管理员添加文档</p>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      title="上传文档"
      :visible.sync="uploadDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      class="upload-dialog">

      <!-- 上传步骤指示器 -->
      <el-steps :active="uploadStep" finish-status="success" style="margin-bottom: 30px;">
        <el-step title="选择文件"></el-step>
        <el-step title="文件信息"></el-step>
        <el-step title="上传设置"></el-step>
        <el-step title="完成上传"></el-step>
      </el-steps>

      <!-- 步骤1: 文件选择 -->
      <div v-if="uploadStep === 0" class="upload-step">
        <div class="upload-area"
             @drop="handleDrop"
             @dragover.prevent
             @dragenter.prevent
             :class="{ 'drag-over': isDragOver }"
             @dragenter="isDragOver = true"
             @dragleave="isDragOver = false">
          <div class="upload-content">
            <i class="el-icon-upload upload-icon"></i>
            <div class="upload-text">
              <p class="upload-title">拖拽文件到此处，或点击选择文件</p>
              <p class="upload-subtitle">支持 PDF、DOC、DOCX、TXT 等格式，单个文件不超过 50MB</p>
            </div>
            <el-button type="primary" @click="selectFiles">选择文件</el-button>
            <input
              ref="fileInput"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.txt,.md"
              @change="handleFileSelect"
              style="display: none;">
          </div>
        </div>

        <!-- 已选择的文件列表 -->
        <div v-if="selectedFiles.length > 0" class="selected-files">
          <h4>已选择的文件 ({{ selectedFiles.length }})</h4>
          <div class="file-list">
            <div v-for="(file, index) in selectedFiles" :key="index" class="file-item">
              <div class="file-info">
                <i class="el-icon-document file-icon"></i>
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">{{ formatFileSize(file.size) }} • {{ file.type || '未知类型' }}</div>
                </div>
              </div>
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="removeFile(index)"
                class="remove-btn">
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 文件信息提取 -->
      <div v-if="uploadStep === 1" class="upload-step">
        <div class="step-header">
          <h3>文件信息提取</h3>
          <p>系统将自动分析文件名并提取相关信息，您也可以手动修改</p>
        </div>

        <div v-if="extractingInfo" class="extracting-info">
          <i class="el-icon-loading" style="font-size: 32px; margin-bottom: 16px;"></i>
          <p>正在分析文件信息...</p>
        </div>

        <div v-else class="file-info-forms">
          <div v-for="(file, index) in selectedFiles" :key="index" class="file-info-form">
            <div class="form-header">
              <i class="el-icon-document"></i>
              <span>{{ file.name }}</span>
            </div>

            <el-form :model="file.extractedInfo" label-width="120px" class="info-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="产品型号">
                    <el-input v-model="file.extractedInfo.型号" placeholder="请输入产品型号"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="产品名称">
                    <el-input v-model="file.extractedInfo.名称" placeholder="请输入产品名称"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="售前售后">
                    <el-select v-model="file.extractedInfo.售前售后" placeholder="请选择">
                      <el-option label="售前" value="售前"></el-option>
                      <el-option label="售后" value="售后"></el-option>
                      <el-option label="通用" value="通用"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="可接入软件">
                    <el-input v-model="file.extractedInfo.可接入软件" placeholder="请输入可接入软件"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 步骤3: 上传设置 -->
      <div v-if="uploadStep === 2" class="upload-step">
        <div class="step-header">
          <h3>上传设置</h3>
          <p>配置文档的处理方式和目标知识库</p>
        </div>

        <el-form :model="uploadSettings" label-width="120px" class="settings-form">
          <el-form-item label="目标知识库" required>
            <el-select v-model="uploadSettings.datasetId" placeholder="请选择知识库" style="width: 100%;">
              <el-option
                v-for="dataset in datasets"
                :key="dataset.id"
                :label="dataset.name"
                :value="dataset.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="训练模式" required>
            <el-radio-group v-model="uploadSettings.trainingType">
              <el-radio label="chunk">自动分块</el-radio>
              <el-radio label="qa">问答拆分</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="分块大小" v-if="uploadSettings.trainingType === 'chunk'">
            <el-input-number
              v-model="uploadSettings.chunkSize"
              :min="500"
              :max="8000"
              :step="100"
              style="width: 200px;">
            </el-input-number>
            <span style="margin-left: 10px; color: #909399;">字符</span>
          </el-form-item>

          <el-form-item label="自定义分割符">
            <el-input
              v-model="uploadSettings.chunkSplitter"
              placeholder="留空使用默认分割符"
              style="width: 300px;">
            </el-input>
          </el-form-item>

          <el-form-item label="处理参数" v-if="uploadSettings.trainingType === 'qa'">
            <el-radio-group v-model="uploadSettings.processingParam">
              <el-radio label="auto">自动处理</el-radio>
              <el-radio label="custom">自定义提示词</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤4: 上传进度 -->
      <div v-if="uploadStep === 3" class="upload-step">
        <div class="step-header">
          <h3>上传进度</h3>
          <p>正在上传文档，请稍候...</p>
        </div>

        <div class="upload-progress">
          <div v-for="(file, index) in selectedFiles" :key="index" class="progress-item">
            <div class="progress-header">
              <span class="file-name">{{ file.name }}</span>
              <span class="progress-status" :class="file.uploadStatus">
                {{ getUploadStatusText(file.uploadStatus) }}
              </span>
            </div>
            <el-progress
              :percentage="file.uploadProgress || 0"
              :status="file.uploadStatus === 'success' ? 'success' : (file.uploadStatus === 'error' ? 'exception' : '')"
              :show-text="false">
            </el-progress>
            <div v-if="file.uploadError" class="error-message">
              {{ file.uploadError }}
            </div>
          </div>

          <div class="overall-progress">
            <h4>总体进度</h4>
            <el-progress :percentage="overallProgress" :status="overallStatus"></el-progress>
          </div>
        </div>
      </div>

      <!-- 对话框底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false" v-if="uploadStep === 0 || uploadStep === 3">取消</el-button>
        <el-button @click="prevStep" v-if="uploadStep > 0 && uploadStep < 3">上一步</el-button>
        <el-button type="primary" @click="nextStep" v-if="uploadStep < 2" :disabled="!canProceed">下一步</el-button>
        <el-button type="primary" @click="startUpload" v-if="uploadStep === 2" :loading="uploading">开始上传</el-button>
        <el-button type="primary" @click="finishUpload" v-if="uploadStep === 3 && allUploadsComplete">完成</el-button>
      </div>
    </el-dialog>

    <!-- 登录对话框 -->
    <el-dialog
      title="管理员登录"
      :visible.sync="loginDialogVisible"
      width="400px"
      :close-on-click-modal="false">
      <el-form :model="loginForm" label-width="80px">
        <el-form-item label="账号">
          <el-input v-model="loginForm.username" placeholder="请输入管理员账号"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" @keyup.enter.native="handleLogin"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="loginDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleLogin" :loading="loginLoading">登 录</el-button>
      </div>
    </el-dialog>

    <!-- 预览抽屉 -->
    <el-drawer
      :title="previewData.name"
      :visible.sync="previewDrawerVisible"
      direction="rtl"
      size="50%"
      class="preview-drawer">
      <div v-if="previewData" style="padding: 0 20px;">
        <div v-if="previewData.q && previewData.q.length > 0">
          <div v-for="(item, index) in previewData.q" :key="index" class="preview-card-container">
            <div class="preview-card" @click="showPreviewQA(item)">
              <div class="preview-id">
                ID: {{ item[0] }}
              </div>
              <div v-html="marked(item[1])"></div>
              <div v-html="marked(item[2])"></div>
              <div v-if="isLoggedIn && previewData.audit == 0" class="preview-delete" @click.stop="deletePreviewItem(index)">
                <i class="el-icon-delete"></i>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state" style="margin: 40px 0;">
          <i class="el-icon-document"></i>
          <h3>暂无内容</h3>
          <p>该文档暂无分块内容</p>
        </div>
      </div>
    </el-drawer>

    <!-- 预览模态框 -->
    <el-dialog
      :title="previewData.name"
      :visible.sync="previewModalVisible"
      width="60%"
      :before-close="handlePreviewModalClose"
      class="preview-modal">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="分块内容" name="content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div style="padding-bottom: 15px;">主要内容</div>
              <el-input
                type="textarea"
                v-model="currentItem[1]"
                :rows="10"
                placeholder="请输入主要内容"
                :disabled="!isLoggedIn">
              </el-input>
            </el-col>
            <el-col :span="12">
              <div style="padding-bottom: 15px;">补充内容</div>
              <el-input
                type="textarea"
                v-model="currentItem[2]"
                :rows="10"
                placeholder="请输入补充内容"
                :disabled="!isLoggedIn">
              </el-input>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane :label="`数据索引(${currentItem[3] ? currentItem[3].length : 0})`" name="index">
          <div style="margin-bottom: 15px;" v-if="isLoggedIn && previewData.audit == 0">
            <el-button type="primary" icon="el-icon-plus" @click="addNewIndex">新增索引</el-button>
          </div>
          <div v-for="(item, index) in currentItem[3]" :key="index" class="index-item">
            <!-- 默认索引 -->
            <div v-if="item && (item.defaultIndex == true || item.type == 'default')" class="default-index">
              <div class="index-content">
                <div style="font-weight: bold; color: #409EFF;">默认索引</div>
                <div>{{ item.text }}</div>
              </div>
            </div>
            <!-- 自定义索引 -->
            <div v-else-if="item" class="custom-index" @click="startEditing(index)">
              <div class="index-content">
                <div style="font-weight: bold;">自定义索引 {{ index + 1 }}</div>
                <el-input
                  v-if="item.editing"
                  v-model="item.text"
                  type="textarea"
                  :rows="3"
                  @blur="item.editing = false"
                  :disabled="!isLoggedIn">
                </el-input>
                <div v-else>{{ item.text }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" v-if="isLoggedIn && previewData.audit == 0">
        <el-button @click="previewModalVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdate">确认更新</el-button>
      </span>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      title="审核文件"
      :visible.sync="auditDialogVisible"
      width="50%"
      class="audit-dialog">
      <div class="audit-dialog-content">
        <el-form :model="auditForm" label-width="80px">
          <el-form-item label="文件名称">
            <el-input v-model="auditForm.fileName" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="审核状态">
            <el-radio-group v-model="auditForm.status">
              <el-radio label="1">审核通过</el-radio>
              <el-radio label="2">审核拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见">
            <el-input
              type="textarea"
              v-model="auditForm.comment"
              :rows="4"
              placeholder="请输入审核意见..."
              maxlength="500"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAudit" :loading="auditLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>

  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          loading: true,
          datasets: [],
          selectedDataset: '',
          currentDataset: {},
          files: [],
          filteredFiles: [],
          searchKeyword: '',
          filterStatus: '',
          uploadDialogVisible: false,
          // 上传相关数据
          uploadStep: 0,
          selectedFiles: [],
          isDragOver: false,
          extractingInfo: false,
          uploading: false,
          uploadSettings: {
            datasetId: '',
            trainingType: 'chunk',
            chunkSize: 4000,
            chunkSplitter: '',
            processingParam: 'auto'
          },
          // 批量选择相关数据
          selectedFileIds: [],
          selectAll: false,
          stats: {
            totalDocs: 0,
            auditedDocs: 0,
            pendingDocs: 0,
            auditRate: 0
          },
          // 登录相关数据
          isLoggedIn: false,
          loginDialogVisible: false,
          loginLoading: false,
          loginForm: {
            username: '',
            password: ''
          },
          authToken: '',
          // 预览相关数据
          previewDrawerVisible: false,
          previewData: { name: '', q: [], audit: 0 },
          previewModalVisible: false,
          activeTab: 'content',
          currentItem: ['', '', '', []],
          // 审核相关数据
          auditDialogVisible: false,
          auditLoading: false,
          auditForm: {
            fileName: '',
            status: '1',
            comment: '',
            fileId: ''
          }
        }
      },
      computed: {
        canProceed() {
          if (this.uploadStep === 0) {
            return this.selectedFiles.length > 0;
          }
          if (this.uploadStep === 1) {
            return !this.extractingInfo;
          }
          if (this.uploadStep === 2) {
            return this.uploadSettings.datasetId && this.uploadSettings.trainingType;
          }
          return false;
        },

        overallProgress() {
          if (this.selectedFiles.length === 0) return 0;
          const totalProgress = this.selectedFiles.reduce((sum, file) => sum + (file.uploadProgress || 0), 0);
          return Math.round(totalProgress / this.selectedFiles.length);
        },

        overallStatus() {
          const hasError = this.selectedFiles.some(file => file.uploadStatus === 'error');
          const allComplete = this.selectedFiles.every(file => file.uploadStatus === 'success');

          if (hasError) return 'exception';
          if (allComplete) return 'success';
          return '';
        },

        allUploadsComplete() {
          return this.selectedFiles.length > 0 &&
                 this.selectedFiles.every(file => file.uploadStatus === 'success' || file.uploadStatus === 'error');
        },

        // 批量选择相关计算属性
        isIndeterminate() {
          const selectedCount = this.selectedFileIds.length;
          const totalCount = this.filteredFiles.length;
          return selectedCount > 0 && selectedCount < totalCount;
        }
      },

      mounted() {
        this.checkLoginStatus();
        this.initPage();
      },
      methods: {
        async initPage() {
          // 从URL参数获取datasetId
          const urlParams = new URLSearchParams(window.location.search);
          const datasetId = urlParams.get('datasetId');
          
          await this.loadDatasets();
          
          if (datasetId) {
            this.selectedDataset = datasetId;
            await this.loadFiles();
          }
        },
        
        async loadDatasets() {
          try {
            const response = await axios.get('/getDatasList/');
            if (response.data.code === 200) {
              this.datasets = response.data.data;
              // 设置当前知识库信息
              if (this.selectedDataset) {
                this.currentDataset = this.datasets.find(d => d.id === this.selectedDataset) || {};
              }
            }
          } catch (error) {
            console.error('加载知识库列表失败:', error);
          }
        },
        
        async loadFiles() {
          if (!this.selectedDataset) return;
          
          try {
            this.loading = true;
            // 这里需要根据实际API调整
            const response = await axios.get(`/getCollectionListInfo/?datasetId=${this.selectedDataset}`);
            if (response.data.code === 200) {
              this.files = response.data.data || [];
              this.updateStats();
              this.filterFiles();
            }
          } catch (error) {
            console.error('加载文档列表失败:', error);
            this.$message.error('加载文档列表失败');
          } finally {
            this.loading = false;
          }
        },
        
        updateStats() {
          this.stats.totalDocs = this.files.length;
          this.stats.auditedDocs = this.files.filter(f => f.audit === '1').length;
          this.stats.pendingDocs = this.files.filter(f => f.audit === '0').length;
          this.stats.auditRate = this.stats.totalDocs > 0 ? 
            Math.round((this.stats.auditedDocs / this.stats.totalDocs) * 100) : 0;
        },
        
        filterFiles() {
          let filtered = [...this.files];
          
          // 状态筛选
          if (this.filterStatus !== '') {
            filtered = filtered.filter(f => f.audit === this.filterStatus);
          }
          
          // 关键词搜索
          if (this.searchKeyword.trim()) {
            const keyword = this.searchKeyword.toLowerCase();
            filtered = filtered.filter(f => 
              f.name.toLowerCase().includes(keyword)
            );
          }
          
          this.filteredFiles = filtered;
        },
        
        onDatasetChange() {
          this.currentDataset = this.datasets.find(d => d.id === this.selectedDataset) || {};
          this.loadFiles();
        },
        
        onSearch() {
          this.filterFiles();
        },
        
        showUploadDialog() {
          if (!this.checkPermission('upload')) {
            return;
          }

          this.resetUploadState();
          this.uploadDialogVisible = true;
          // 设置默认知识库
          if (this.selectedDataset) {
            this.uploadSettings.datasetId = this.selectedDataset;
          }
        },

        resetUploadState() {
          this.uploadStep = 0;
          this.selectedFiles = [];
          this.isDragOver = false;
          this.extractingInfo = false;
          this.uploading = false;
          this.uploadSettings = {
            datasetId: this.selectedDataset || '',
            trainingType: 'chunk',
            chunkSize: 4000,
            chunkSplitter: '',
            processingParam: 'auto'
          };
        },

        // 文件选择相关方法
        selectFiles() {
          this.$refs.fileInput.click();
        },

        handleFileSelect(event) {
          const files = Array.from(event.target.files);
          this.addFiles(files);
          event.target.value = ''; // 清空input，允许重复选择同一文件
        },

        handleDrop(event) {
          event.preventDefault();
          this.isDragOver = false;
          const files = Array.from(event.dataTransfer.files);
          this.addFiles(files);
        },

        addFiles(files) {
          const validFiles = files.filter(file => {
            // 检查文件类型
            const validTypes = ['.pdf'];
            const fileExt = '.' + file.name.split('.').pop().toLowerCase();
            if (!validTypes.includes(fileExt)) {
              this.$message.warning(`文件 ${file.name} 格式不支持`);
              return false;
            }

            // 检查文件大小 (50MB)
            if (file.size > 50 * 1024 * 1024) {
              this.$message.warning(`文件 ${file.name} 超过50MB限制`);
              return false;
            }

            // 检查是否已存在
            if (this.selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
              this.$message.warning(`文件 ${file.name} 已存在`);
              return false;
            }

            return true;
          });

          // 添加文件并初始化状态
          validFiles.forEach(file => {
            file.extractedInfo = {
              型号: '',
              名称: '',
              售前售后: '',
              可接入软件: ''
            };
            file.uploadProgress = 0;
            file.uploadStatus = 'pending';
            file.uploadError = '';
            this.selectedFiles.push(file);
          });

          if (validFiles.length > 0) {
            this.$message.success(`成功添加 ${validFiles.length} 个文件`);
          }
        },

        removeFile(index) {
          this.selectedFiles.splice(index, 1);
        },

        formatFileSize(bytes) {
          if (bytes === 0) return '0 B';
          const k = 1024;
          const sizes = ['B', 'KB', 'MB', 'GB'];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        async viewFile(file) {
          // 预览文件详情
          try {
            const response = await axios.get('/getDatasetdatas/', {
              params: { collectionId: file.id }
            });
            if (response.data.code === 200) {
              this.previewData = response.data.data;
              this.previewDrawerVisible = true;
            } else {
              this.$message.error('获取文件数据失败');
            }
          } catch (error) {
            console.error('获取文件数据失败:', error);
            this.$message.error('获取文件数据失败');
          }
        },
        
        editFile(file) {
          // 编辑文件
          console.log('编辑文件:', file);
        },
        
        deleteFile(file) {
          if (!this.checkPermission('delete')) {
            return;
          }

          // 删除文件
          this.$confirm('确定要删除这个文档吗？此操作不可恢复！', '提示', {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error'
          }).then(async () => {
            try {
              const response = await axios.post('/deleteCollection/', {
                collectionId: file.id,
                token: this.getToken()
              });

              if (response.data.code === 200) {
                this.$message.success('删除成功');
                this.loadFiles();
              } else {
                this.$message.error(response.data.message || '删除失败');
              }
            } catch (error) {
              console.error('删除文件失败:', error);
              this.$message.error('删除失败，请稍后重试');
            }
          }).catch(() => {
            // 用户取消删除
          });
        },
        
        formatDate(dateStr) {
          if (!dateStr) return '未知';
          const date = new Date(dateStr);
          return date.toLocaleDateString('zh-CN');
        },

        // 步骤控制方法
        nextStep() {
          if (this.uploadStep === 0 && this.selectedFiles.length > 0) {
            this.uploadStep = 1;
            this.extractFileInfo();
          } else if (this.uploadStep === 1) {
            this.uploadStep = 2;
          }
        },

        prevStep() {
          if (this.uploadStep > 0) {
            this.uploadStep--;
          }
        },

        // 文件信息提取
        async extractFileInfo() {
          this.extractingInfo = true;

          try {
            for (let file of this.selectedFiles) {
              try {
                // 增加文件地址参数
                console.log(file)
                const response = await axios.get(`/getFileInfo/${encodeURIComponent(file.name)}`, {
                  params: {
                    path: file.path || ''
                  }
                });
                if (response.data.code === 200) {
                  file.extractedInfo = { ...response.data.data };
                }
              } catch (error) {
                console.error(`提取文件 ${file.name} 信息失败:`, error);
                // 使用默认值
                file.extractedInfo = {
                  型号: '未知',
                  名称: '未知',
                  售前售后: '未知',
                  可接入软件: '未知'
                };
              }
            }
          } finally {
            this.extractingInfo = false;
          }
        },

        // 开始上传
        async startUpload() {
          this.uploadStep = 3;
          this.uploading = true;

          try {
            for (let i = 0; i < this.selectedFiles.length; i++) {
              const file = this.selectedFiles[i];
              await this.uploadSingleFile(file, i);
            }
          } finally {
            this.uploading = false;
          }
        },

        // 上传单个文件
        async uploadSingleFile(file, index) {
          file.uploadStatus = 'uploading';
          file.uploadProgress = 0;

          try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', this.uploadSettings.datasetId);

            const uploadData = {
              trainingType: this.uploadSettings.trainingType,
              chunkSize: this.uploadSettings.chunkSize,
              chunkSplitter: this.uploadSettings.chunkSplitter,
              processingParam: this.uploadSettings.processingParam,
              fileInfo: file.extractedInfo
            };

            formData.append('data', JSON.stringify(uploadData));

            const response = await axios.post('/uploadfiles/', formData, {
              headers: {
                'Content-Type': 'multipart/form-data'
              },
              onUploadProgress: (progressEvent) => {
                file.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              }
            });

            if (response.data.code === 200) {
              file.uploadStatus = 'success';
              file.uploadProgress = 100;
            } else {
              throw new Error(response.data.message || '上传失败');
            }
          } catch (error) {
            file.uploadStatus = 'error';
            file.uploadError = error.response?.data?.detail || error.message || '上传失败';
            console.error(`文件 ${file.name} 上传失败:`, error);
          }
        },

        // 完成上传
        finishUpload() {
          this.uploadDialogVisible = false;
          this.loadFiles(); // 刷新文件列表

          const successCount = this.selectedFiles.filter(f => f.uploadStatus === 'success').length;
          const errorCount = this.selectedFiles.filter(f => f.uploadStatus === 'error').length;

          if (errorCount === 0) {
            this.$message.success(`成功上传 ${successCount} 个文件`);
          } else {
            this.$message.warning(`上传完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
          }
        },

        // 获取上传状态文本
        getUploadStatusText(status) {
          const statusMap = {
            pending: '等待中',
            uploading: '上传中',
            success: '上传成功',
            error: '上传失败'
          };
          return statusMap[status] || '未知状态';
        },

        // 批量选择相关方法
        handleSelectAll(value) {
          if (value) {
            this.selectedFileIds = this.filteredFiles.map(file => file.id);
          } else {
            this.selectedFileIds = [];
          }
        },

        // 批量操作处理
        handleBatchAction(command) {
          if (this.selectedFileIds.length === 0) {
            this.$message.warning('请先选择要操作的文档');
            return;
          }

          switch (command) {
            case 'batchApprove':
              this.batchAudit('approve');
              break;
            case 'batchReject':
              this.batchAudit('reject');
              break;
            case 'batchDelete':
              this.batchDelete();
              break;
          }
        },

        // 批量审核
        async batchAudit(action) {
          const actionText = action === 'approve' ? '审核通过' : '审核拒绝';

          try {
            await this.$confirm(`确定要${actionText}选中的 ${this.selectedFileIds.length} 个文档吗？`, '批量审核', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: action === 'approve' ? 'success' : 'warning'
            });

            let rejectReason = '';
            if (action === 'reject') {
              const { value } = await this.$prompt('请输入拒绝原因', '审核拒绝', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPlaceholder: '请输入拒绝原因...'
              });
              rejectReason = value || '';
            }

            const response = await axios.post('/auditCollection/', {
              collectionIds: this.selectedFileIds,
              action: action,
              rejectReason: rejectReason,
              token: this.getToken()
            });

            if (response.data.code === 200 || response.data.code === 207) {
              this.$message.success(response.data.message);
              this.selectedFileIds = [];
              this.selectAll = false;
              this.loadFiles();
            } else {
              this.$message.error(response.data.message || '操作失败');
            }

          } catch (error) {
            if (error !== 'cancel') {
              console.error('批量审核失败:', error);
              this.$message.error('操作失败，请稍后重试');
            }
          }
        },

        // 批量删除
        async batchDelete() {
          try {
            await this.$confirm(`确定要删除选中的 ${this.selectedFileIds.length} 个文档吗？此操作不可恢复！`, '批量删除', {
              confirmButtonText: '确定删除',
              cancelButtonText: '取消',
              type: 'error'
            });

            let successCount = 0;
            let errorCount = 0;

            for (const fileId of this.selectedFileIds) {
              try {
                const response = await axios.get(`/deleteCollection/?id=${fileId}`);
                if (response.data.code === 200) {
                  successCount++;
                } else {
                  errorCount++;
                }
              } catch (error) {
                errorCount++;
                console.error(`删除文档 ${fileId} 失败:`, error);
              }
            }

            if (errorCount === 0) {
              this.$message.success(`成功删除 ${successCount} 个文档`);
            } else {
              this.$message.warning(`删除完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
            }

            this.selectedFileIds = [];
            this.selectAll = false;
            this.loadFiles();

          } catch (error) {
            if (error !== 'cancel') {
              console.error('批量删除失败:', error);
              this.$message.error('删除失败，请稍后重试');
            }
          }
        },

        // 单个文件审核 - 审核通过
        approveFile(file) {
          this.showAuditDialog(file, '1');
        },

        // 单个文件审核 - 审核拒绝
        rejectFile(file) {
          this.showAuditDialog(file, '2');
        },

        // 显示审核对话框
        showAuditDialog(file, status = '1') {
          if (!this.checkPermission('audit')) {
            return;
          }

          this.auditForm = {
            fileName: file.name,
            status: status,
            comment: '',
            fileId: file.id
          };
          this.auditDialogVisible = true;
        },

        // 确认审核
        async confirmAudit() {
          if (!this.auditForm.comment.trim()) {
            this.$message.warning('请输入审核意见');
            return;
          }

          try {
            this.auditLoading = true;
            const response = await axios.post('/auditCollection/', {
              collectionId: this.auditForm.fileId,
              action: this.auditForm.status === '1' ? 'approve' : 'reject',
              rejectReason: this.auditForm.comment,
              comment: this.auditForm.comment,
              token: this.getToken()
            });

            if (response.data.code === 200) {
              const statusText = this.auditForm.status === '1' ? '审核通过' : '审核拒绝';
              this.$message.success(`${statusText}成功`);
              this.auditDialogVisible = false;
              this.loadFiles();
            } else {
              this.$message.error(response.data.message || '审核失败');
            }
          } catch (error) {
            console.error('审核失败:', error);
            this.$message.error('审核失败，请稍后重试');
          } finally {
            this.auditLoading = false;
          }
        },

        // 获取审核状态样式类
        getStatusClass(audit) {
          switch (audit) {
            case '1': return 'status-approved';
            case '2': return 'status-rejected';
            default: return 'status-pending';
          }
        },

        // 获取审核状态文本
        getStatusText(audit) {
          switch (audit) {
            case '1': return '已审核';
            case '2': return '已拒绝';
            default: return '待审核';
          }
        },

        // 获取认证token
        getToken() {
          return this.authToken || localStorage.getItem('authToken') || '';
        },

        // 通用API请求方法
        async apiRequest(url, options = {}) {
          const config = {
            ...options,
            headers: {
              'Content-Type': 'application/json',
              ...options.headers
            }
          };

          // 如果是需要权限的请求，添加token
          if (this.isLoggedIn && this.getToken()) {
            config.headers['Authorization'] = `Bearer ${this.getToken()}`;
          }

          try {
            const response = await axios(url, config);

            // 检查是否是权限错误
            if (response.data.code === 401 || response.data.code === 403) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
              return null;
            }

            return response;
          } catch (error) {
            if (error.response && error.response.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
              return null;
            }
            throw error;
          }
        },

        // 检查是否有权限执行操作
        checkPermission(action = 'default') {
          if (!this.isLoggedIn) {
            this.$message.warning('请先登录后再进行此操作');
            return false;
          }
          return true;
        },

        // 检查登录状态
        checkLoginStatus() {
          const token = localStorage.getItem('authToken');
          const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
          if (token && isLoggedIn) {
            this.authToken = token;
            this.isLoggedIn = true;
          }
        },

        // 显示登录对话框
        showLoginDialog() {
          this.loginDialogVisible = true;
          this.loginForm = {
            username: '',
            password: ''
          };
        },

        // 处理登录
        async handleLogin() {
          if (!this.loginForm.username || !this.loginForm.password) {
            this.$message.warning('请输入账号和密码');
            return;
          }

          try {
            this.loginLoading = true;
            const response = await axios.post('/login/', {
              username: this.loginForm.username,
              password: this.loginForm.password
            });

            if (response.data.code === 200) {
              this.$message.success('登录成功');
              this.authToken = response.data.token;
              this.isLoggedIn = true;
              this.loginDialogVisible = false;

              // 保存到本地存储
              localStorage.setItem('authToken', response.data.token);
              localStorage.setItem('isLoggedIn', 'true');

              // 重新加载数据以显示管理员功能
              this.loadFiles();
            } else {
              this.$message.error('登录失败：' + (response.data.message || '账号或密码错误'));
            }
          } catch (error) {
            console.error('登录失败:', error);
            this.$message.error('登录失败，请稍后重试');
          } finally {
            this.loginLoading = false;
          }
        },

        // 处理退出登录
        handleLogout() {
          this.$confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.authToken = '';
            this.isLoggedIn = false;

            // 清除本地存储
            localStorage.removeItem('authToken');
            localStorage.removeItem('isLoggedIn');

            this.$message.success('退出成功');

            // 重新加载数据以隐藏管理员功能
            this.loadFiles();
          }).catch(() => {
            // 用户取消退出
          });
        },

        // Markdown渲染方法
        marked(text) {
          if (!text) return '';
          return marked.parse(text);
        },

        // 显示预览QA编辑模态框
        showPreviewQA(item) {
          this.previewModalVisible = true;
          this.currentItem = [...item]; // 复制数组避免直接修改
        },

        // 关闭预览模态框
        handlePreviewModalClose() {
          this.previewModalVisible = false;
          this.currentItem = ['', '', '', []];
          this.activeTab = 'content';
        },

        // 删除预览数据中的项
        async deletePreviewItem(index) {
          if (!this.isLoggedIn) {
            this.$message.warning('请先登录');
            return;
          }

          try {
            const confirmResult = await this.$confirm('确认删除该条数据吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            });

            const response = await axios.post('/deleteQA/', {
              id: this.previewData.q[index][0],
              token: this.getToken()
            });

            if (response.data.code === 200) {
              this.previewData.q.splice(index, 1);
              this.$message.success('删除成功');
            } else {
              this.$message.error('删除失败');
            }
          } catch (error) {
            if (error !== 'cancel') {
              console.error('删除失败:', error);
              this.$message.error('删除失败');
            }
          }
        },

        // 开始编辑索引
        startEditing(index) {
          if (!this.isLoggedIn) {
            this.$message.warning('请先登录');
            return;
          }
          this.$set(this.currentItem[3][index], 'editing', !this.currentItem[3][index].editing);
        },

        // 添加新索引
        addNewIndex() {
          if (!this.isLoggedIn) {
            this.$message.warning('请先登录');
            return;
          }
          this.currentItem[3].push({
            text: '',
            editing: true,
            type: 'custom'
          });
        },

        // 确认更新
        async confirmUpdate() {
          if (!this.isLoggedIn) {
            this.$message.warning('请先登录');
            return;
          }

          try {
            const response = await axios.post('/updateDatasetdatas/', {
              data: this.currentItem,
              token: this.getToken()
            });

            if (response.data.code === 200) {
              this.$message.success('更新成功');
              this.previewModalVisible = false;
              // 重新加载预览数据
              this.viewFile({ id: this.previewData.collectionId });
            } else {
              this.$message.error('更新失败');
            }
          } catch (error) {
            console.error('更新数据失败:', error);
            this.$message.error('更新失败');
          }
        },

      },
      
      watch: {
        filterStatus() {
          this.filterFiles();
        },

        selectedFileIds() {
          // 更新全选状态
          const selectedCount = this.selectedFileIds.length;
          const totalCount = this.filteredFiles.length;
          this.selectAll = selectedCount === totalCount && totalCount > 0;
        },

        filteredFiles() {
          // 当筛选结果变化时，清空选择
          this.selectedFileIds = [];
          this.selectAll = false;
        }
      }
    });
  </script>
</body>
</html>
