"""
数据集相关API路由
"""
from fastapi import APIRouter, Query
from backend.services.dataset_service import dataset_service
from backend.services.audit_service import audit_service
from backend.models.schemas import AuditRequest

router = APIRouter()


@router.get("/getDatasList/")
async def get_datasets_list():
    """获取数据集列表"""
    return await dataset_service.get_datasets_list()


@router.get("/getHomeDatasetsInfo/")
async def get_home_datasets_info():
    """获取首页知识库信息"""
    return await dataset_service.get_home_datasets_info()


@router.get("/getSameNameFiles/")
async def get_same_name_files(cpxh: str = Query(...), excludeId: str = Query(...)):
    """获取相同型号的文件"""
    return await dataset_service.get_same_name_files(cpxh, excludeId)


@router.get("/getCollectionListInfo/")
async def get_collection_list_info(
    keyword: str = Query(""),
    startDate: str = Query(""),
    endDate: str = Query(""),
    status: str = Query(""),
    datasetId: str = Query("")
):
    """获取集合列表信息"""
    return await dataset_service.get_collection_list_info(keyword, startDate, endDate, status, datasetId)


@router.get("/getDatasetdatas/")
async def get_dataset_datas(collectionId: str = Query(...)):
    """获取数据集数据"""
    return await dataset_service.get_dataset_datas(collectionId)


@router.post("/updateDatasetdatas/")
async def update_dataset_datas(body: dict):
    """更新数据集数据"""
    return await dataset_service.update_dataset_datas(body)


@router.get("/deleteCollection/")
async def delete_collection(id: str = Query(...)):
    """删除集合"""
    return await dataset_service.delete_collection(id)


@router.post("/deleteQA/")
async def delete_qa(body: dict):
    """删除QA数据"""
    return await dataset_service.delete_qa(body)


@router.post("/auditCollection/")
async def audit_collection(audit_request: AuditRequest):
    """审核文档"""
    return await audit_service.audit_collection(audit_request)
