"""
索引同步服务模块
"""
import json
import requests
from config import apiIP, headers, system_prompt, bs_prompt
from backend.models.database import db_manager
from backend.services.ai_service import ai_service
from backend.utils.image_utils import get_image


class IndexService:
    """索引同步服务类"""

    def get_indexes(self, indexes: list, tag: int = 0) -> list:
        """处理索引数据"""
        result = []
        for index in indexes:
            if index.get("defaultIndex") == True or index.get("type") == "default":
                index["_id"] = str(index["_id"])
                result.append(index)
            else:
                if tag == 1:
                    text = {"text": index["text"]}
                else:
                    text = {"text": index["text"], "editing": False}
                result.append(text)
        return result

    def sync_indexes(self):
        """同步索引"""
        try:
            collections = list(db_manager.db["dataset_collections"].find({"metadata.isindex": "0"}))
            for coll in collections:
                metadata = coll.get("metadata", {})
                if metadata.get("source") == "bs":
                    prompt = bs_prompt
                else:
                    prompt = system_prompt
                
                dataids_list = list(db_manager.db['dataset_datas'].find(
                    {"collectionId": coll["_id"]}
                ).sort("chunkIndex", 1))
                
                # 如果已经有数据正在处理，则跳过
                if dataids_list and db_manager.check_training_exists(str(coll["_id"])):
                    continue
                
                for data in dataids_list:
                    try:
                        newindexs = self.get_indexes(data["indexes"], 1)
                        data["q"] = get_image(data["q"])

                        name = "文件名为：" + coll["name"]
                        if coll.get("cpxh"):
                            name += "，产品型号为：" + coll["cpxh"]
                        if coll.get("cpmc"):
                            name += "，产品名称为：" + coll["cpmc"]

                        content = """%s 中的片段内容为 %s""" % (name, data['q'])

                        gjzsd, gjc = ai_service.get_indexes(content, prompt)
                        if not gjzsd and not gjc:
                            continue
                        
                        newindexs.append({"text": gjzsd})
                        newindexs.append({"text": gjc})
                        if coll.get("cpxh") and coll.get("cpmc"):
                            newindexs.append({"text": f"{coll['cpxh']}{coll['cpmc']}"})

                        update_data = {
                            "dataId": str(data["_id"]),
                            "q": data['q'],
                            "a": data['a'],
                            "indexes": newindexs
                        }
                        
                        url = f'http://{apiIP}:3000/api/core/dataset/data/update'
                        update_headers = {
                            'Authorization': 'Bearer fastgpt-e5Lf0kDHo9kXA29cgNVroVWfdNhlw2Csx3ErmS0A0ISfhJyh1EL6eP1HNEqGIX4o',
                            'Content-Type': 'application/json'
                        }
                        response = requests.put(url, headers=update_headers, data=json.dumps(update_data))
                        
                    except Exception as e:
                        print(f"处理数据时发生错误: {e}")
                
                # 标记为已索引
                db_manager.update_collection(str(coll["_id"]), {"metadata.isindex": "1"})
                
        except Exception as e:
            print(f"同步索引时发生错误: {e}")


# 全局索引服务实例
index_service = IndexService()

# 导出同步函数以保持向后兼容
def sync_indexs():
    """同步索引函数 - 保持向后兼容"""
    return index_service.sync_indexes()
