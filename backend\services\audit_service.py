"""
审核服务模块
"""
from datetime import datetime, timezone
from fastapi import HTTPException
from bson.objectid import ObjectId
from config import baseusername, basepassword
from backend.models.database import db_manager
from backend.models.schemas import AuditRequest, APIResponse
from backend.services.auth_service import AuthService


class AuditService:
    """审核服务类"""
    
    def __init__(self):
        self.global_datasets_dic = {}
        self.global_datasets_collections_dic = {}
    
    def get_global_data(self):
        """获取全局数据"""
        datasets = db_manager.get_datasets()
        self.global_datasets_dic = {str(d["_id"]): d["name"] for d in datasets}
        
        collections = db_manager.get_dataset_collections()
        self.global_datasets_collections_dic = {str(d["_id"]): d["name"] for d in collections}
    
    async def audit_collection(self, audit_request: AuditRequest) -> APIResponse:
        """优化后的文档审核函数"""
        try:
            # 参数验证
            collection_ids = audit_request.collectionIds or []
            if audit_request.collectionId and not collection_ids:
                collection_ids = [audit_request.collectionId]

            if not collection_ids:
                return APIResponse(code=400, message="请选择要审核的文档")

            # 权限验证
            if not AuthService.verify_token(audit_request.token):
                return APIResponse(code=403, message="无权限进行审核操作")

            if not self.global_datasets_dic or not self.global_datasets_collections_dic:
                self.get_global_data()

            success_count = 0
            error_count = 0
            error_messages = []

            for coll_id in collection_ids:
                try:
                    # 验证collection是否存在
                    collection_obj = db_manager.get_collection_by_id(coll_id)
                    if not collection_obj:
                        error_messages.append(f"文档 {coll_id} 不存在")
                        error_count += 1
                        continue

                    if audit_request.action == "approve":
                        # 审核通过
                        update_data = {
                            "metadata.audit": "1",
                            "metadata.auditTime": datetime.now(timezone.utc).isoformat(),
                            "metadata.auditBy": baseusername,
                            "forbid": False
                        }

                        # 更新collection状态
                        db_manager.update_collection(coll_id, update_data)

                        # 写入知识清单
                        dataset_id = str(collection_obj["datasetId"])
                        create_time = collection_obj["createTime"]

                        # 检查是否已存在于清单中
                        existing_record = db_manager.db["files_list_wh"].find_one({"collectionId": coll_id})
                        if not existing_record:
                            db_manager.insert_file_list({
                                "datasetId": dataset_id,
                                "datasetname": self.global_datasets_dic.get(dataset_id, "未知知识库"),
                                "collectionId": coll_id,
                                "name": self.global_datasets_collections_dic.get(coll_id, collection_obj.get("name", "未知文档")),
                                "creatTime": create_time,
                                "auditTime": datetime.now(timezone.utc),
                                "metadata": collection_obj.get("metadata", {})
                            })

                    elif audit_request.action == "reject":
                        # 审核拒绝
                        update_data = {
                            "metadata.audit": "2",  # 2表示拒绝
                            "metadata.auditTime": datetime.now(timezone.utc).isoformat(),
                            "metadata.auditBy": baseusername,
                            "metadata.rejectReason": audit_request.rejectReason,
                            "forbid": True
                        }

                        # 更新collection状态
                        db_manager.update_collection(coll_id, update_data)

                        # 从知识清单中移除（如果存在）
                        db_manager.delete_file_list(coll_id)

                    success_count += 1

                except Exception as e:
                    error_messages.append(f"处理文档 {coll_id} 时出错: {str(e)}")
                    error_count += 1
                    print(f"审核文档 {coll_id} 失败: {str(e)}")

            # 构建响应消息
            action_text = "审核通过" if audit_request.action == "approve" else "审核拒绝"
            
            if error_count == 0:
                message = f"成功{action_text}了 {success_count} 个文档"
                return APIResponse(
                    code=200, 
                    message=message, 
                    data={"success": success_count, "error": error_count}
                )
            else:
                message = f"处理完成：成功 {success_count} 个，失败 {error_count} 个"
                return APIResponse(
                    code=207,  # 部分成功
                    message=message,
                    data={
                        "success": success_count,
                        "error": error_count,
                        "errorMessages": error_messages
                    }
                )

        except Exception as e:
            print(f"审核操作异常: {str(e)}")
            return APIResponse(code=500, message=f"服务器内部错误: {str(e)}")


# 全局审核服务实例
audit_service = AuditService()
