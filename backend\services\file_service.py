"""
文件处理服务模块
"""
import json
import requests
from datetime import datetime, timezone
from fastapi import UploadFile, HTTPException
from urllib.parse import quote
from bson.objectid import ObjectId
from config import (apiIP, headers, zhishiku_sm_dict, tishici_dict, 
                   zhishiku_ss_dict, templatesIP)
from backend.models.database import db_manager
from backend.models.schemas import APIResponse
from backend.utils.validation import validate_upload_file
from backend.utils.file_utils import build_qa_prompt, get_filename_from_url
from backend.services.ai_service import ai_service


class FileService:
    """文件处理服务类"""
    
    def __init__(self):
        self.global_datasets_dic = {}
        self.global_datasets_collections_dic = {}
    
    def get_global_data(self):
        """获取全局数据"""
        datasets = db_manager.get_datasets()
        self.global_datasets_dic = {str(d["_id"]): d["name"] for d in datasets}
        
        collections = db_manager.get_dataset_collections()
        self.global_datasets_collections_dic = {str(d["_id"]): d["name"] for d in collections}
    
    async def upload_files(self, file: UploadFile, file_type: str, data: str) -> APIResponse:
        """优化后的文件上传处理函数"""
        try:
            # 文件验证
            validation_result = await validate_upload_file(file)
            if not validation_result["valid"]:
                return APIResponse(code=400, message=validation_result["message"])

            # 获取全局数据
            if file_type not in self.global_datasets_dic:
                self.get_global_data()

            # 验证知识库是否存在
            if file_type not in self.global_datasets_dic:
                return APIResponse(code=400, message="指定的知识库不存在")

            dataset_name = self.global_datasets_dic[file_type]
            file_content = await file.read()

            # 解析处理参数
            try:
                process_params = json.loads(data)
            except json.JSONDecodeError:
                return APIResponse(code=400, message="处理参数格式错误")

            # 验证必要参数
            required_params = ["trainingType", "chunkSize"]
            for param in required_params:
                if param not in process_params:
                    return APIResponse(code=400, message=f"缺少必要参数: {param}")

            training_type = process_params["trainingType"]

            # 构建QA提示词
            dataset_sm = zhishiku_sm_dict.get(dataset_name, "")
            qa_prompt = tishici_dict.get(dataset_name, "")

            if training_type == "qa":
                qa_prompt = build_qa_prompt(dataset_name, dataset_sm, file.filename, qa_prompt)

            qa_prompt_tag = training_type == "qa" and process_params.get("processingParam") == "custom"

            # 构建上传数据
            upload_data = {
                "datasetId": file_type,
                "parentId": None,
                "trainingType": training_type,
                "chunkSize": process_params["chunkSize"],
                "chunkSplitter": process_params.get("chunkSplitter", ""),
                "qaPrompt": qa_prompt if qa_prompt_tag else "",
                "metadata": {
                    "source": "api",
                    "audit": "0",
                    "size": file.size,
                    "uploadTime": datetime.now(timezone.utc).isoformat(),
                    "originalName": file.filename
                }
            }

            # 添加文件信息到metadata
            file_info = process_params.get("fileInfo", {})
            if file_info:
                upload_data["metadata"].update({
                    "型号": file_info.get("型号", "未知"),
                    "名称": file_info.get("名称", "未知"),
                    "售前售后": file_info.get("售前售后", "未知"),
                    "可接入软件": file_info.get("可接入软件", "未知")
                })

            # 调用FastGPT API上传文件
            upload_result = await self.upload_to_fastgpt(file, file_content, upload_data)

            if upload_result["success"]:
                # 更新本地数据库
                await self.update_local_database(upload_result["collectionId"], file, upload_data, file_info)

                # 异步同步到北京API（如果需要）
                if dataset_name in zhishiku_ss_dict:
                    parent_id = zhishiku_ss_dict.get(dataset_name, '')
                    self.sync_beijing_api(file_content, file.content_type, qa_prompt, file.filename, parent_id)

                return APIResponse(
                    code=200, 
                    message="文件上传成功", 
                    data={"collectionId": upload_result["collectionId"]}
                )
            else:
                return APIResponse(code=upload_result["code"], message=upload_result["message"])

        except Exception as e:
            print(f"文件上传异常: {str(e)}")
            return APIResponse(code=500, message=f"服务器内部错误: {str(e)}")
    
    async def upload_to_fastgpt(self, file: UploadFile, file_content: bytes, upload_data: dict) -> dict:
        """上传文件到FastGPT API"""
        try:
            data_json = json.dumps(upload_data)
            files = {
                "file": (quote(file.filename), file_content, file.content_type),
                "data": (None, data_json, "application/json")
            }

            response = requests.post(
                f"http://{apiIP}:3000/api/core/dataset/collection/create/localFile",
                headers=headers,
                files=files,
                timeout=300  # 5分钟超时
            )

            response_data = response.json()

            if response_data.get("code") == 200:
                return {
                    "success": True,
                    "collectionId": response_data["data"]["collectionId"]
                }
            else:
                return {
                    "success": False,
                    "code": response_data.get("code", 500),
                    "message": response_data.get("message", "FastGPT API调用失败")
                }

        except requests.exceptions.Timeout:
            return {"success": False, "code": 408, "message": "上传超时，请稍后重试"}
        except requests.exceptions.RequestException as e:
            return {"success": False, "code": 500, "message": f"网络请求失败: {str(e)}"}
        except Exception as e:
            return {"success": False, "code": 500, "message": f"上传过程中发生错误: {str(e)}"}
    
    async def update_local_database(self, collection_id: str, file: UploadFile, upload_data: dict, file_info: dict):
        """更新本地数据库记录"""
        try:
            # 更新全局变量
            self.global_datasets_collections_dic[collection_id] = file.filename

            # 获取collection记录
            collection = db_manager.get_collection_by_id(collection_id)
            if not collection:
                print(f"警告: 未找到collection记录 {collection_id}")
                return

            # 更新metadata
            metadata = collection.get("metadata", {})
            metadata.update({
                "source": "api",
                "audit": "0",
                "isindex": "0",
                "size": file.size,
                "uploadTime": datetime.now(timezone.utc).isoformat(),
                "originalName": file.filename
            })

            # 添加文件信息
            if file_info:
                metadata.update({
                    "型号": file_info.get("型号", "未知"),
                    "名称": file_info.get("名称", "未知"),
                    "售前售后": file_info.get("售前售后", "未知"),
                    "可接入软件": file_info.get("可接入软件", "未知")
                })

            # 使用AI提取产品型号
            cpxh = ai_service.extract_product_model(collection["name"])
            if cpxh:
                metadata["cpxh"] = cpxh

            # 更新数据库记录
            db_manager.update_collection(collection_id, {
                "metadata": metadata, 
                "forbid": True
            })

            print(f"成功更新collection {collection_id} 的本地数据库记录")

        except Exception as e:
            print(f"更新本地数据库失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    def sync_beijing_api(self, file_content: bytes, content_type: str, qa_prompt: str, filename: str, parent_id: str):
        """同步到北京API"""
        try:
            data = {
                "datasetId": "6739d0b48e3e1b37c294c883",  # 知识库ID
                "parentId": parent_id,  # 父级ID
                "trainingType": "qa",  # 训练模式
                "chunkSize": 4000,  # chunk长度
                "chunkSplitter": "",  # 自定义分割符号
                "qaPrompt": qa_prompt  # qa拆分提示词
            }
            data_json = json.dumps(data)
            files = {
                "file": (quote(filename), file_content, content_type),
                "data": (None, data_json, "application/json")
            }
            response = requests.post(
                f"http://{apiIP}:3000/api/core/dataset/collection/create/localFile",
                headers=headers,
                files=files
            ).json()
        except Exception as e:
            print(f"同步北京API失败: {e}")
    
    async def save_bs(self, body: dict) -> APIResponse:
        """保存标书文件"""
        try:
            url_list = body.get("urllist", [])
            collection_list = []
            file_content = b""
            filename = ""
            content_type = ""
            doc_tag = False
            
            for url in url_list:
                file_url = "https://zktecoaihub.com" + url
                response = requests.get(file_url)

                # 获取文件名
                filename_from_url = get_filename_from_url(file_url)
                file_content += response.content
                
                if not doc_tag:
                    if "doc" in filename_from_url:
                        doc_tag = True
                    filename = filename_from_url
                    content_type = response.headers.get("Content-Type", "application/octet-stream")

            data = {
                "datasetId": "6777ca0d9f5a5d92bd1c6fac",  # 知识库ID
                "parentId": "",  # 父级ID
                "trainingType": "chunk",  # 训练模式
                "chunkSize": 1800,  # chunk长度
                "chunkSplitter": "",  # 自定义分割符号
                "qaPrompt": ""  # qa拆分提示词
            }
            data_json = json.dumps(data)
            files = {
                "file": (quote(filename), file_content, content_type),
                "data": (None, data_json, "application/json")
            }
            
            response = requests.post(
                f"http://{apiIP}:3000/api/core/dataset/collection/create/localFile",
                headers=headers,
                files=files
            ).json()
            
            if response["code"] == 200:
                collection_list.append(response["data"]["collectionId"])
                collection_id = ObjectId(response["data"]["collectionId"])
                
                collection = db_manager.get_collection_by_id(str(collection_id))
                if collection:
                    metadata = collection.get("metadata", {})
                    metadata["source"] = "bs"
                    metadata["isindex"] = "0"
                    db_manager.update_collection(str(collection_id), {"metadata": metadata})
            else:
                raise HTTPException(status_code=response["code"], detail=str(response))
            
            return APIResponse(code=200, data=collection_list)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


# 全局文件服务实例
file_service = FileService()
