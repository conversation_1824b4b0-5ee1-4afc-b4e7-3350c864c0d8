"""
数据集服务模块
"""
import json
import requests
from datetime import datetime, timezone
from fastapi import HTTPException
from bson.objectid import ObjectId
from config import dataids_list, apiIP, headers
from backend.models.database import db_manager
from backend.models.schemas import DatasetInfo, CollectionInfo, APIResponse
from backend.utils.image_utils import get_image, del_image


class DatasetService:
    """数据集服务类"""
    
    def __init__(self):
        self.global_datasets_dic = {}
        self.global_datasets_collections_dic = {}
    
    def get_global_data(self):
        """获取全局数据"""
        self.global_datasets_dic = {}
        self.global_datasets_collections_dic = {}
        
        datasets = db_manager.get_datasets()
        self.global_datasets_dic = {str(d["_id"]): d["name"] for d in datasets}
        
        collections = db_manager.get_dataset_collections()
        self.global_datasets_collections_dic = {str(d["_id"]): d["name"] for d in collections}
    
    async def get_datasets_list(self) -> APIResponse:
        """获取数据集列表"""
        try:
            if not self.global_datasets_dic or not self.global_datasets_collections_dic:
                self.get_global_data()
            
            datasets_list = [
                {"id": k, "name": v} 
                for k, v in self.global_datasets_dic.items()
            ]
            return APIResponse(code=200, data=datasets_list)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_home_datasets_info(self) -> APIResponse:
        """获取首页知识库信息"""
        try:
            # 获取所有活跃的知识库，不限制父级
            datasets = list(db_manager.db['datasets'].find({
                "type": "dataset"
            }).sort("updateTime", -1))

            datasets_info = []
            for dataset in datasets:
                # 统计每个知识库下的文档数量
                doc_count = db_manager.db['dataset_collections'].count_documents({
                    "datasetId": dataset["_id"],
                    "type": {"$ne": "folder"}
                })

                # 统计已审核的文档数量
                audited_count = db_manager.db['dataset_collections'].count_documents({
                    "datasetId": dataset["_id"],
                    "type": {"$ne": "folder"},
                    "metadata.audit": "1"
                })

                # 格式化更新时间
                update_time = dataset.get("updateTime", "")
                if update_time and hasattr(update_time, 'isoformat'):
                    update_time = update_time.isoformat()
                elif update_time:
                    update_time = str(update_time)

                dataset_info = DatasetInfo(
                    id=str(dataset["_id"]),
                    name=dataset["name"],
                    intro=dataset.get("intro", ""),
                    avatar=dataset.get("avatar", "core/dataset/commonDatasetColor"),
                    updateTime=update_time,
                    docCount=doc_count,
                    auditedCount=audited_count,
                    vectorModel=dataset.get("vectorModel", ""),
                    agentModel=dataset.get("agentModel", "")
                )
                datasets_info.append(dataset_info.dict())

            return APIResponse(code=200, data=datasets_info)
        except Exception as e:
            print(f"获取首页知识库信息失败: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_same_name_files(self, cpxh: str, exclude_id: str) -> APIResponse:
        """获取相同型号的文件"""
        try:
            query = {
                "_id": {"$ne": ObjectId(exclude_id)},
                "datasetId": {"$in": dataids_list},
                "type": "file",
                "metadata.cpxh": {"$regex": cpxh, "$options": "i"}
            }
            collections = db_manager.db['dataset_collections'].find(query).sort("createTime", -1)
            result = []
            for collection in collections:
                result.append({
                    "id": str(collection["_id"]),
                    "datasetId": str(collection["datasetId"]),
                    "name": collection["name"],
                    "time": collection["createTime"],
                    "audit": collection["metadata"].get("audit", "1")
                })
            return APIResponse(code=200, data=result)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_collection_list_info(self, keyword: str = "", start_date: str = "", 
                                     end_date: str = "", status: str = "", 
                                     dataset_id: str = "") -> APIResponse:
        """获取集合列表信息"""
        try:
            if not self.global_datasets_dic or not self.global_datasets_collections_dic:
                self.get_global_data()

            query = {"metadata.source": "api"}
            if dataset_id:
                query["datasetId"] = ObjectId(dataset_id)
            if keyword:
                query["name"] = {"$regex": f".*{keyword}.*"}
            if start_date and end_date:
                query["$and"] = [
                    {"createTime": {"$gte": datetime.fromisoformat(start_date).replace(tzinfo=timezone.utc)}},
                    {"createTime": {"$lte": datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)}}
                ]
            if status:
                query["metadata.audit"] = status
            
            collections = db_manager.db['dataset_collections'].find(query)
            result = []
            for collection in collections:
                dataset_id_str = str(collection["datasetId"])
                dataset_name = self.global_datasets_dic.get(dataset_id_str, "未知知识库")
                
                collection_info = CollectionInfo(
                    id=str(collection["_id"]),
                    datasetId=dataset_id_str,
                    datasetname=dataset_name,
                    name=collection["name"],
                    time=collection["createTime"],
                    updateTime=collection["createTime"],
                    audit=collection["metadata"]["audit"],
                    cpxh=collection["metadata"].get("cpxh", ""),
                    size=collection["metadata"]["size"],
                    description=collection.get("intro", "")
                )
                result.append(collection_info.dict())
            
            return APIResponse(code=200, data=result)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    def get_indexes(self, indexes: list, tag: int = 0) -> list:
        """处理索引数据"""
        result = []
        for index in indexes:
            if index.get("defaultIndex") == True or index.get("type") == "default":
                index["_id"] = str(index["_id"])
                result.append(index)
            else:
                if tag == 1:
                    text = {"text": index["text"]}
                else:
                    text = {"text": index["text"], "editing": False}
                result.append(text)
        return result
    
    async def get_dataset_datas(self, collection_id: str) -> APIResponse:
        """获取数据集数据"""
        try:
            try:
                collection = db_manager.get_collection_by_id(collection_id)
                audit = collection["metadata"]["audit"] if collection else "1"
            except:
                audit = "1"
            
            dataids_list = db_manager.get_dataset_datas(collection_id)
            result = []
            for data in dataids_list:
                indexes = self.get_indexes(data["indexes"])
                data["q"] = get_image(data["q"])
                result.append([str(data["_id"]), data["q"], data["a"] or '', indexes])
            
            collection_name = self.global_datasets_collections_dic.get(collection_id, "未知文档")
            return APIResponse(code=200, data={
                "name": collection_name,
                "q": result,
                "audit": audit
            })
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    async def update_dataset_datas(self, body: dict) -> APIResponse:
        """更新数据集数据"""
        try:
            data_id = body["data"][0]
            q = body["data"][1]
            a = body["data"][2] or ''
            indexes = body["data"][3]
            
            if not data_id:
                return APIResponse(code=500, message="参数错误")
            
            new_indexes = []
            for index in indexes:
                if not index.get("text"):
                    continue
                if "editing" in index:
                    del index["editing"]
                new_indexes.append(index)
            
            # 去除图片参数
            q = del_image(q)
            data = {"dataId": data_id, "q": q, "a": a, "indexes": new_indexes}
            
            headers_copy = headers.copy()
            headers_copy["Content-Type"] = "application/json"
            response = requests.post(
                f"http://{apiIP}:3000/api/core/dataset/data/update",
                data=json.dumps(data),
                headers=headers_copy
            ).json()
            
            if response["code"] == 200:
                return APIResponse(code=200)
            else:
                raise HTTPException(status_code=response["code"], detail=str(response))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    async def delete_collection(self, collection_id: str) -> APIResponse:
        """删除集合"""
        try:
            response = requests.delete(
                f"http://{apiIP}:3000/api/core/dataset/collection/delete?id=" + collection_id,
                headers=headers
            ).json()
            
            if response["code"] == 200:
                return APIResponse(code=200)
            else:
                raise HTTPException(status_code=response["code"], detail=str(response))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    async def delete_qa(self, body: dict) -> APIResponse:
        """删除QA数据"""
        try:
            data_id = body.get("id", "")
            if not data_id:
                return APIResponse(code=500, message="参数错误")
            
            response = requests.delete(
                f"http://{apiIP}:3000/api/core/dataset/data/delete?id=" + data_id,
                headers=headers
            ).json()
            
            if response["code"] == 200:
                return APIResponse(code=200)
            else:
                raise HTTPException(status_code=response["code"], detail=str(response))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


# 全局数据集服务实例
dataset_service = DatasetService()
