"""
文件相关API路由
"""
from fastapi import APIRouter, File, Form, UploadFile, Path, Query
from backend.services.file_service import file_service
from backend.services.ai_service import ai_service

router = APIRouter()


@router.post("/uploadfiles/")
async def upload_files(
    file: UploadFile = File(...), 
    type: str = Form(...), 
    data: str = Form(...)
):
    """文件上传"""
    return await file_service.upload_files(file, type, data)


@router.get("/getFileInfo/{filename}")
async def get_file_info(
    filename: str = Path(...),
    path: str = Query('', description="文件路径")
):
    """获取文件信息"""
    file_content = ''
    if filename.lower().endswith('.pdf'):
        # 优先使用前端传入的path参数
        file_path = path
        if file_path:
            try:
                with open(file_path, "rb") as f:
                    file_content = f.read()
            except Exception as e:
                return {"error": f"无法读取文件: {str(e)}"}
    return await ai_service.get_file_info(filename, file_content)

@router.post("/savebs/")
async def save_bs(body: dict):
    """保存标书文件"""
    return await file_service.save_bs(body)
