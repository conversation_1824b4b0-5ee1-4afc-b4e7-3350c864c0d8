"""
图片处理工具模块
"""
import re
from config import apiIP


def get_image(q: str) -> str:
    """将图片链接转换为HTML格式"""
    pattern = r'!\[([^\]]*)\]\((/api/system/img/[^)]+\.(?:png|jpg|jpeg))\)'
    replacement = f'<image src="http://{apiIP}:3000' + r'\2" alt="\1" loading="lazy" referrerpolicy="no-referrer"/>'
    new_string = re.sub(pattern, replacement, q)
    return new_string


def del_image(q: str) -> str:
    """将HTML图片格式还原为Markdown格式"""
    # 定义正则表达式模式
    pattern = f'<image src="http://{apiIP}:3000' + r'(/api/system/img/[^"]+\.(?:png|jpg|jpeg))" alt="([^"]*)" loading="lazy" referrerpolicy="no-referrer"/>'
    # 还原为 ![](...)
    replacement = r'![\2](\1)'
    # 使用 re.sub 进行还原
    original_string = re.sub(pattern, replacement, q)
    return original_string
